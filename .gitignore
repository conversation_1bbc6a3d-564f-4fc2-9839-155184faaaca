###> symfony/framework-bundle ###
/.env.local.php
/.env.*.local
/config/secrets/prod/prod.decrypt.private.php
/public/bundles/
/var/
/vendor/
/node_modules/
/public/build/
###< symfony/framework-bundle ###

.devcontainer/data

###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
###< phpunit/phpunit ###

###> symfony/phpunit-bridge ###
.phpunit.result.cache
/phpunit.xml
###< symfony/phpunit-bridge ###
.DS_Store
.idea
###> friendsofphp/php-cs-fixer ###
/.php-cs-fixer.php
/.php-cs-fixer.cache
###< friendsofphp/php-cs-fixer ###

.cf-vars.*.local.yaml
.env.local

docker/local/postgresql/data
!docker/local/postgresql/data/.gitkeep

docker/local/s3storage/data
!docker/local/s3storage/data/.gitkeep

###> phpstan/phpstan ###
phpstan.neon
###< phpstan/phpstan ###

.augment-guidelines
.clinerules
auth.json
