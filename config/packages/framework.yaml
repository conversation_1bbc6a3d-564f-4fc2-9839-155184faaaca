# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    annotations: false
    secret: '%env(APP_SECRET)%'
    #csrf_protection: true
    http_method_override: true
    handle_all_throwables: true
    uid:
      default_uuid_version: 7
      time_based_uuid_version: 7

    # Enables session support. Note that the session will ONLY be started if you read or write from it.
    # Remove or comment this section to explicitly disable session support.
    session:
        enabled: true
        handler_id: null
        cookie_secure: auto
        cookie_samesite: lax
        storage_factory_id: session.storage.factory.native

    #esi: true
    #fragments: true
    php_errors:
        log: true

    property_info:
        with_constructor_extractor: true

    trusted_hosts: '%env(TRUSTED_HOSTS)%'
    trusted_proxies: '%env(TRUSTED_PROXIES)%'
    trusted_headers: ['x-forwarded-for', 'x-forwarded-host', 'x-forwarded-proto']
