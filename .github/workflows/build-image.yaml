name: Build Release Image

on:
  release:
    types:
      - created

run-name: Build Release Image (${{ github.event.release.tag_name }})

jobs:
  build-release-images:
    name: Build release images
    uses: prezero/workflows/.github/workflows/docker-build.yaml@106c3b692d7f52f14532ec5287f80f7bd192bcb2 # v1.40.5
    with:
      ENV: prod
      DOCKERFILE: Dockerfile
      IMAGE: ghcr.io/${{ github.repository }}
      TAG: ${{ github.event.release.tag_name }}
      RELEASE_CREATED: true
    secrets: inherit
