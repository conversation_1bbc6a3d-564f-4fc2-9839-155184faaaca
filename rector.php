<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

use <PERSON>\CodeQuality\Rector\Identical\FlipTypeControlToUseExclusiveTypeRector;
use <PERSON>\CodeQuality\Rector\Switch_\SwitchTrueToIfRector;
use <PERSON>\CodingStyle\Rector\If_\NullableCompareToNullRector;
use <PERSON>\Config\RectorConfig;
use <PERSON>\Doctrine\Set\DoctrineSetList;
use <PERSON>\Php74\Rector\Property\RestoreDefaultNullToNullableTypePropertyRector;
use <PERSON>\Php80\Rector\Class_\ClassPropertyAssignToConstructorPromotionRector;
use <PERSON>\Php81\Rector\FuncCall\NullToStrictStringFuncCallArgRector;
use Rector\Php83\Rector\ClassMethod\AddOverrideAttributeToOverriddenMethodsRector;
use Rector\TypeDeclaration\Rector\StmtsAwareInterface\DeclareStrictTypesRector;
use <PERSON><PERSON><PERSON><PERSON>hail\AddNamedArgumentsRector\AddNamedArgumentsRector;

return RectorConfig::configure()
    ->withPaths([
        __DIR__.'/src',
        __DIR__.'/tests',
    ])
    ->withSkip([
        __DIR__.'/config/bundles.php',
        __DIR__.'/tests/_output',
        __DIR__.'/tests/Support/_generated',
        RestoreDefaultNullToNullableTypePropertyRector::class,
        NullToStrictStringFuncCallArgRector::class,
        FlipTypeControlToUseExclusiveTypeRector::class,
        AddOverrideAttributeToOverriddenMethodsRector::class,
        ClassPropertyAssignToConstructorPromotionRector::class => [
            __DIR__.'/src/Entity/*/*.php',
        ],
        SwitchTrueToIfRector::class,
        AddNamedArgumentsRector::class => [
            __DIR__.'/src/Entity/*/*.php',
        ],
    ])
    ->withImportNames(importShortClasses: false, removeUnusedImports: true)
    ->withRules([
        DeclareStrictTypesRector::class,
        NullableCompareToNullRector::class,
        AddNamedArgumentsRector::class,
    ])
    ->withPhpSets()
    ->withPreparedSets(
        deadCode: true,
        codeQuality: true,
        typeDeclarations: true,
    )
    ->withAttributesSets(
        symfony: true,
        doctrine: true,
    )
    ->withComposerBased(
        doctrine: true,
        symfony: true,
    )
    ->withSets([
        DoctrineSetList::DOCTRINE_CODE_QUALITY,
        DoctrineSetList::TYPED_COLLECTIONS,
        DoctrineSetList::YAML_TO_ANNOTATIONS,
    ]);
