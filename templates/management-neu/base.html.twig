<!DOCTYPE html>
<html lang="de">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="apple-touch-icon" sizes="180x180" href="data:image/png;base64,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">
    <link rel="icon" type="image/png" sizes="32x32" href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAABuVBMVEUAAACW4QCV4gAAMz0AMDoAB1sAPzUAMjua4QAALTwAMjyX4gAAAE6W4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCa5gBipBcALT8AMz0AMz0AMz0AKkAAMj0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz2W4QAAMz2W4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QAAMz0AMz0AMz0AMz2W4QCW4QCW4QCW4QCW4QAAMz0AMz0AMz0AMz0AMz0AMz0AMz1ytw+X4wCW4QCW4QCW4QCW4QCW4QCW4QAAMj0AMz0AMz0AMz0HOzqY4wCW4QCW4QAAMz0AMz0AMz0AMz0AMz0AMz2W4QCW4QCW4QCW4QCW4QCW4QAAMz2W4QD///8s0S0aAAAAkHRSTlMAAAAAAAAAAAAAAAAAAh5Pb3h3KxeA3f38/lolus5+UUdEjBS2+YtgW3OQCM/YHzv2VmdT+FgTUiAjKR8GNrfn6+GZGsvs4/KVA0z8zzQgIR5W89wbWqwkaquW9Dcj3MoSCpdtuQos6RuT+q8RL/avm5zDujFMTUyF1LMhA3Pv2Eai+9cFO2xyYiU8cmlIGQFd2b1tAAAAAWJLR0SSlgTvIAAAAAd0SU1FB+MCBwkoDZcuQc0AAAE2SURBVDjLY2AYLICRl49fQBADCAkzQqQZRUTFxMUl0IG4pBQjRF5aRlxWTl4BDSgqwRQoq6iqiTAyYtirLqnBCDZAU1xLmxGLw2AKlHV09RgZ8CjQN9BixKvAUNKIkQmfAg1JdWwGUFOBsYkpfgVm5haWVmBgbcPMgqmAlc3Wzt4BDBydnJnZMRQwO7tMcHUDA/cJHp7MmAq8vH18/fyBICAwKDgERQE4sphDJ4RB9DEHONqHwxUwMkaIG0MVRGJVwBEVHYNXQWxcfAIeBYyJSeLJ4EhkDpyQksrMycDAxZyW7uYPUcDInZGZlZ0DDkDmXPu8/IJCICgomlBcUloGBOWGFZXiVdWQVMBcU1s3AQrqGxonioOAZFOzoDIslTC3tLa1g0FHJ0+XZjcI9PT29WONoAEBANRqVMi3YztfAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE5LTAyLTA3VDA5OjQwOjEzKzAxOjAwL+3nhwAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxOS0wMi0wN1QwOTo0MDoxMyswMTowMF6wXzsAAABXelRYdFJhdyBwcm9maWxlIHR5cGUgaXB0YwAAeJzj8gwIcVYoKMpPy8xJ5VIAAyMLLmMLEyMTS5MUAxMgRIA0w2QDI7NUIMvY1MjEzMQcxAfLgEigSi4A6hcRdPJCNZUAAAAASUVORK5CYII=">
    <link rel="icon" type="image/png" sizes="16x16" href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAABF1BMVEUAAACW4QCT4ACX4QCX4wCY5AACNzkAMz0AMjwAMzwAMj2W4gAAMjmU4ACW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCW4QCQ2wCX4gCY5ABTlBsOQzcAMj0AMz0AMz0AMz0AMz0AMT4AMj0AMz0AMz0AMz0AMz0AMz0AMz0AMz0AMz2W4QCW4QAAMz0AMz2W4QCW4QAAMz0AMz0AMz0AMz0AMj0USjWh7gCW4QCW4QCW4QCW4QCW4QAAMz0AMz0AMz0AMz0AMD5kpxSX4wCW4QCW4QCW4QAAMz0AMz0AKkCf6wD///88OibfAAAAXHRSTlMAAAAAAAAAAAAAAAAAAAMGBQ9knKKpcgENlZZFNTJpmwJcmQlAmJFOAo06NoCLi4xpD4K8hIXIZgGjY4dQkGKDClqRpVhat4UzODdIkgwpm6+ujFGdoWEOAgQBARYmjHsAAAABYktHRFzq2ACXAAAAB3RJTUUH4wIHCSgNly5BzQAAAKxJREFUGNNtz8UOwgAQRdEZbHCKO5Ti7u7uroX//w8glAQCd3mSt3gAvyFqdUKMFkGEeoPRJGS2WAFtdofT9crt8bKAPs6P73EgyII4FEbJJ7DBAMInRKIx6RfI4olkKp3J0hsoly8Ui6UyyQWoVGukoEKdlKDCRhMf0CI1tZ+AHW8XgXr9wXA4Gk+mszm3WD5gtd5sN7v94XjkTmfUgIIuPM9fbwzDsIh/vt8B9P0YdseVWDIAAAAldEVYdGRhdGU6Y3JlYXRlADIwMTktMDItMDdUMDk6NDA6MTMrMDE6MDAv7eeHAAAAJXRFWHRkYXRlOm1vZGlmeQAyMDE5LTAyLTA3VDA5OjQwOjEzKzAxOjAwXrBfOwAAAFd6VFh0UmF3IHByb2ZpbGUgdHlwZSBpcHRjAAB4nOPyDAhxVigoyk/LzEnlUgADIwsuYwsTIxNLkxQDEyBEgDTDZAMjs1Qgy9jUyMTMxBzEB8uASKBKLgDqFxF08kI1lQAAAABJRU5ErkJggg==">
    <link rel="mask-icon" href="{{asset('/build/icons/safari-pinned-tab.svg')}}" color="#5bbad5">
    <link rel="shortcut icon" href="{{asset('/build/icons/favicon.ico')}}">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-config" content="{{asset('/build/icons/browserconfig.xml')}}">
    <meta name="theme-color" content="#ffffff">

    <title>{% block title %}PreZero{% endblock %}</title>

    {% block stylesheets %}
        {{ encore_entry_link_tags('tailwind') }}
        {{ encore_entry_link_tags('management') }}
    {% endblock %}

  </head>

  <body>
    <header class="shadow-lg">
      <div class="container mx-auto px-4 flex justify-between items-center bg-white">
        <div class="flex justify-between gap-5">
          <a href="{% if not is_granted('ROLE_ADMIN') and not is_granted('ROLE_MANAGER') and not is_granted('ROLE_REPORT') %}{{ path('app_default_index') }}{% else %}{{ path('management') }}{% endif %}"><img src="{{asset('/build/svg/prezero_logo.svg')}}" alt="" class="py-5"></a>
          <div class="flex space-x-5 justify-between items-stretch font-extralight ">
              <a href="{{ path('management_order_report') }}" class="flex items-center border-transparent border-b-2 hover:border-pzblue hover:text-pzblue transition duration-150">{{ 'to_the_portal'|trans }}</a>
              {#  <a href="{{ path('app_default_ordernewclearance') }}" class="flex items-center border-transparent border-b-2 hover:border-pzblue hover:text-pzblue transition duration-150">Auftrag melden</a>
              <a href="{{ path('app_default_documentsview') }}" class="flex items-center border-transparent border-b-2 hover:border-pzblue hover:text-pzblue transition duration-150">Wiegescheine</a> #}
          </div>
        </div>
        <div class="flex space-x-5 justify-between items-center font-extralight">
          <div class="relative inline-block text-left">
            <div class="account-button">
              <button type="button" class="flex items-center space-x-4 w-full bg-white font-extralight hover:text-pzblue">
                <img src="{{asset('/build/svg/PreZero_Bildmarke.svg')}}" alt="">
                <span class="hidden sm:block">{{ 'account'|trans }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <div class="account-dropdown origin-top-right right-0 w-60  hidden absolute rounded-md shadow-pz-default bg-white overflow-hidden">
                  {% if is_granted('IS_AUTHENTICATED_FULLY') %}
                    <p class="block px-4 pt-2 font-thin">{{ 'logged_in_as'|trans }}</p>
                    <p class="block px-4 py-2 font-extralight border-b-2">{{ app.user.username }}</p>

                      {% if is_granted('ROLE_MANAGER') or is_granted('ROLE_REPORT') %}
                          <a href="{{ path('app_login') }}" class="block px-4 py-2 hover:bg-gray-50"><i class="fas fa-arrow-circle-left mr-2 text-pzpetrol"></i>{{ 'to_the_portal'|trans }}</a>
                          {% if is_granted('ROLE_MANAGER') %}
                              <a href="{{ path('settings_dashboard') }}" class="block px-4 py-2 hover:bg-gray-50 border-b-2"><i class="fas fa-cog mr-2 text-pzpetrol"></i>{{ 'old_management'|trans }}</a>
                              <a href="{{ path('management_contractArea') }}" class="block px-4 py-2 hover:bg-gray-50"><i class="fas fa-map-marked-alt mr-2 text-pzpetrol"></i>{{ 'contract_area_management'|trans }}</a>
                              <a href="{{ path('management_collectingplace') }}" class="block px-4 py-2 hover:bg-gray-50"><i class="fas fa-house mr-2 text-pzpetrol"></i>{{ 'collecting_places_management'|trans }}</a>
                              <a href="{{ path('management_unloadingpoint') }}" class="block px-4 py-2 hover:bg-gray-50"><i class="fas fa-industry mr-2 text-pzpetrol"></i>{{ 'unloading_point_management'|trans }}</a>
                              <a href="{{ path('management_user') }}" class="block px-4 py-2 hover:bg-gray-50"><i class="fas fa-users mr-2 text-pzpetrol"></i>{{ 'user_management'|trans }}</a>
                          {% endif %}
                          {% if is_granted('ROLE_REPORT') %}
                              <a href="{{ path('management_reporting') }}" class="block px-4 py-2 rounded-b-md hover:bg-gray-50 "><i class="fas fa-search-location mr-2 text-pzpetrol"></i>{{ 'reporting'|trans }}</a>
                          {% endif %}
                      {% endif %}
                      <a href="{{ path('app_logout') }}" class="block px-4 py-2 rounded-b-md hover:bg-gray-50 border-t-2"><i class="fas fa-sign-out-alt mr-2 text-pzpetrol"></i>{{ 'logout'|trans }}</a>
                  {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    {% block popup %}{% endblock %}

    {% if backUrl is defined %}
        <section class="flex items-center container mx-auto px-4 mt-2 mb-10">
            <i class="fas fa-angle-left mr-2 text-xl text-pzgreen"></i>
            <a href="{{ backUrl }}" class="font-extralight text-pzgreen">{{ 'return'|trans }}</a>
        </section>
    {% endif %}

    {% block body %}{% endblock %}

    <footer>
      {# <div class="container mx-auto px-4 flex justify-between items-center space-x-10">
        <div class="container mx-auto py-10 bg-white shadow-pz-default rounded-xl font-extralight flex flex-col justify-center md:flex-row mb-10 divide-y-2 md:divide-y-0 md:divide-x-2 divide-pzgrey divide-solid">
        <a href="#" class="text-xl text-center font-extralight mb-5 md:mb-0 px-10 md:px-10 lg:px-20 xl:px-32 2xl:px-40">
          Lorem Ipsum
        </a>
        <a href="#" class="text-xl text-center font-extralight mb-5 pt-5 md:pt-0 md:mb-0 md:px-10 lg:px-20 xl:px-32 2xl:px-40">
          Lorem Ipsum
        </a>
        <a href="#" class="text-xl text-center font-extralight pt-5 md:pt-0 md:mb-0 md:px-10 lg:px-20 xl:px-32 2xl:px-40">
          Lorem Ipsum
        </a>
        </div>
      </div>  #}
      <div class="container mx-auto py-5 px-4 lg:px-10 flex flex-col md:flex-row justify-center mb-5">
        <img src="{{asset('/build/svg/PreZero_Bildmarke.svg')}}" alt="" class="px-4 md:px-0 w-1/3 md:w-auto mb-5">
        <p class="text-base font-extralight mb-2 md:mb-0 px-4 lg:px-10">
            PreZero Recycling Deutschland<br> GmbH & Co. KG
        </p>
        <p class="text-base font-thin mb-2 md:mb-0 px-4 lg:px-10">
            Auf der Plaße 1 <br>
            32469 Petershagen
        </p>
        <p class="text-base text-pzgreen font-thin px-4 lg:px-10">
            <a href="mailto:<EMAIL>"><EMAIL></a>
        </p>
      </div>
      <div class="bg-[#EFEFEF] py-2 md:p-2">
        <div class="container mx-auto px-4 font-extralight text-[#818181] flex flex-col sm:flex-row justify-start">
            <a href="{{ path('app_contact_contact') }}" class="px-4">{{ 'contact'|trans }}</a>
            <a href="{{ path('app_default_dataprivacy') }}" class="px-4">{{ 'privacy_policy'|trans }}</a>
            <a href="{{ path('app_default_compliance') }}" class="px-4">{{ 'compliance'|trans }}</a>
            <a href="{{ path('app_default_imprint') }}" class="px-4">{{ 'legal_notice'|trans }}</a>
        </div>
      </div>

        <div class="bg-pzwarning"></div>
    </footer>

    {% block javascripts %}
        {{ encore_entry_script_tags('management-noty') }}
    {% endblock %}
  </body>
</html>
