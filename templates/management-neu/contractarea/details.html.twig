{% extends('management-neu/base.html.twig') %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('management-contractarea-details') }}
{% endblock %}

{% block popup %}{% endblock %}

{% block body %}
    <section class="container mx-auto px-4">
        <h1 class="font-thin text-lg mb-10 border-b-2 border-pzgreen">
            Vertragsgebietsverwaltung
        </h1>

        <div class="bg-white shadow-lg rounded-xl pb-1 mb-10 font-extralight">
            <div class="bg-gradient-to-tr from-pzpetrol-dark via-pzpetrol-light to-pzblue-light px-10 py-5 shadow-lg rounded-t-xl mb-5 font-extralight">
                <h3 class="text-pzgreen font-thin text-xl">{{'collectingplace.contract_area'|trans}}</h3>
                <p class="text-white font-thin">{{ contractArea.name }}</p>
            </div>

            {% block content %}
                {% embed 'management-neu/default/pzdetailsection_skeleton.html.twig' with { title: null } %}
                    {% block content %}
                        {% embed 'management-neu/default/pzdetailline_skeleton.html.twig' %}
                            {% block item1 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'contractArea.field.name'|trans, value: contractArea.name })}}
                            {% endblock %}
                            {% block item2 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'contractArea.field.state'|trans, value: contractArea.state.name })}}
                            {% endblock %}
                        {% endembed %}
                    {% endblock %}
                {% endembed %}

                {% embed 'management-neu/default/pzdetailsection_skeleton.html.twig' with { title: null } %}
                    {% block content %}
                        {% embed 'management-neu/default/pzdetailline_skeleton.html.twig' %}
                            {% block item1 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'contractArea.field.validFrom'|trans, value: contractArea.validFrom|date('d.m.Y') })}}
                            {% endblock %}
                            {% block item2 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'contractArea.field.validTo'|trans, value: contractArea.validTo|date('d.m.Y') })}}
                            {% endblock %}
                        {% endembed %}
                    {% endblock %}
                {% endembed %}

                {% if 'contingents' in app.request.get('_route') %}
                    <div class="mt-10 px-10">
                        <div>
                            <div class="hidden sm:block">
                                <div class="border-b border-pzgrey-border mb-10">
                                    <nav class="-mb-px flex" aria-label="Tabs">
                                        {#
                                        <a href="{{ path('management_contractArea_details_contingents', { 'uuid': app.request.attributes.get('_route_params').uuid }) }}" class="border-pzgreen text-pzgreen w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg">
                                            {{'collectingplace.quotas'|trans}}
                                        </a>
                                        #}
                                        <a href="{{ path('management_contractArea_details_collectingplaces', { 'uuid': app.request.attributes.get('_route_params').uuid }) }}" class="border-transparent text-pzgrey-border hover:text-pzgrey-dark hover:border-pzgrey-border w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg">
                                            {{'collectingplace.collecting_places'|trans}}
                                        </a>
                                        <a href="{{ path('management_contractArea_details_contracts', { 'uuid': app.request.attributes.get('_route_params').uuid }) }}" class="border-transparent text-pzgrey-border hover:text-pzgrey-dark hover:border-pzgrey-border w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg">
                                            {{'collectingplace.contracts'|trans}}
                                        </a>
                                    </nav>
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-col lg:flex-row justify-between mb-5">
                            <div class="mb-5">
                                <h3 class="text-xl font-extralight mb-2">{{'collectingplace.quotas'|trans}}</h3>
                                <p class="font-thin">{{'collectingplace.quotas.overview_text'|trans}}</p>
                            </div>
                        </div>
                    </div>

                    <div class="px-10 mb-5">
                        <div id="filters-table-contractArea-contingents"></div>
                        <table id="contractArea-contingents" class="blackgrid-contingents theme-prezero"
                               data-source='{{ list|escape('html_attr') }}'
                               data-columns="{{ columns|json_encode|escape('html_attr') }}">
                        </table>
                    </div>
                {% elseif 'collectingplaces' in app.request.get('_route') %}
                    <div class="mt-10 px-10">
                        <div>
                            <div class="hidden sm:block">
                                <div class="border-b border-pzgrey-border mb-10">
                                    <nav class="-mb-px flex" aria-label="Tabs">
                                        {#
                                        <a href="{{ path('management_contractArea_details_contingents', { 'uuid': app.request.attributes.get('_route_params').uuid }) }}" class="border-transparent text-pzgrey-border hover:text-pzgrey-dark hover:border-pzgrey-border w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg">
                                            Kontingente
                                        </a>
                                        #}
                                        <a href="{{ path('management_contractArea_details_collectingplaces', { 'uuid': app.request.attributes.get('_route_params').uuid }) }}" class="border-pzgreen text-pzgreen w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg">
                                            Umschläge
                                        </a>
                                        <a href="{{ path('management_contractArea_details_contracts', { 'uuid': app.request.attributes.get('_route_params').uuid }) }}" class="border-transparent text-pzgrey-border hover:text-pzgrey-dark hover:border-pzgrey-border w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg">
                                            Verträge
                                        </a>
                                    </nav>
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-col lg:flex-row justify-between mb-5">
                            <div class="mb-5">
                                <h3 class="text-xl font-extralight mb-2">{{'collectingplace.collecting_places'|trans}}</h3>
                                <p class="font-thin">{{'collectingplace.collecting_places.overview_text'|trans}}</p>
                            </div>
                        </div>
                    </div>

                    <div class="px-10 mb-5">
                        <div id="filters-table-contractArea-contingents"></div>
                        <table id="contractArea-contingents" class="blackgrid-contingents theme-prezero"
                               data-source='{{ list|escape('html_attr') }}'
                               data-columns="{{ columns|json_encode|escape('html_attr') }}">
                        </table>
                    </div>
                {% elseif 'contracts' in app.request.get('_route') %}
                    <div class="mt-10 px-10">
                        <div>
                            <div class="hidden sm:block">
                                <div class="border-b border-pzgrey-border mb-10">
                                    <nav class="-mb-px flex" aria-label="Tabs">
                                        {#
                                        <a href="{{ path('management_contractArea_details_contingents', { 'uuid': app.request.attributes.get('_route_params').uuid }) }}" class="border-transparent text-pzgrey-border hover:text-pzgrey-dark hover:border-pzgrey-border w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg">
                                            Kontingente
                                        </a>
                                        #}
                                        <a href="{{ path('management_contractArea_details_collectingplaces', { 'uuid': app.request.attributes.get('_route_params').uuid }) }}" class="border-transparent text-pzgrey-border hover:text-pzgrey-dark hover:border-pzgrey-border w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg">
                                            Umschläge
                                        </a>
                                        <a href="{{ path('management_contractArea_details_contracts', { 'uuid': app.request.attributes.get('_route_params').uuid }) }}" class="border-pzgreen text-pzgreen w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg">
                                            Verträge
                                        </a>
                                    </nav>
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-col lg:flex-row justify-between mb-5">
                            <div class="mb-5">
                                <h3 class="text-xl font-extralight mb-2">{{'collectingplace.contracts'|trans}}</h3>
                                <p class="font-thin">{{'collectingplace.contracts.overview_text'|trans}}</p>
                            </div>
                        </div>
                    </div>

                    <div class="px-10 mb-5">
                        <div id="filters-table-contractArea-contingents"></div>
                        <table id="contractArea-contingents" class="blackgrid-contingents theme-prezero"
                               data-source='{{ list|escape('html_attr') }}'
                               data-columns="{{ columns|json_encode|escape('html_attr') }}">
                        </table>
                    </div>
                {% endif %}
            {% endblock %}

        </div>
    </section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('management-contractarea-details') }}
{% endblock %}
