{% extends('management-neu/base.html.twig') %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('management-unloadingpoint-details') }}
{% endblock %}

{% block popup %}
    {{  include('management-neu/unloadingpoint/edit_popup.html.twig', { title: 'user.details.edit'|trans, editForm: editForm }) }}
{% endblock %}

{% block body %}
    <section class="container mx-auto px-4">
        <h1 class="font-thin text-lg mb-10 border-b-2 border-pzgreen">
            Anlagenverwaltung
        </h1>


        {% embed 'management-neu/default/pzdetails_skeleton.html.twig' with { title: 'Anlage' } %}
            {% block actions %}
                {% if is_granted('ROLE_MANAGER') %}
                    {{ include('management-neu/default/pzbutton.html.twig', { id: 'edit_contingent_button', icon: 'fa-edit', title: 'Kontingent bearbeiten' })}}
                {% endif %}
            {% endblock %}


            {% block content %}
                {% embed 'management-neu/default/pzdetailsection_skeleton.html.twig' with { title: null } %}
                    {% block content %}
                        {% embed 'management-neu/default/pzdetailline_skeleton.html.twig' %}
                            {% block item1 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'unloadingPoint.field.dsdid'|trans, value: unloadingPoint.dsdId })}}
                            {% endblock %}
                            {% block item2 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'unloadingPoint.field.esaid'|trans, value: unloadingPoint.unloadingPointId })}}
                            {% endblock %}
                        {% endembed %}
                    {% endblock %}
                {% endembed %}

                {% embed 'management-neu/default/pzdetailsection_skeleton.html.twig' with { title: null } %}
                    {% block content %}
                        {% embed 'management-neu/default/pzdetailline_skeleton.html.twig' %}
                            {% block item1 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'unloadingPoint.field.name'|trans, value: unloadingPoint.name1 })}}
                            {% endblock %}
                            {% block item2 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'unloadingPoint.field.state'|trans, value: unloadingPoint.state.name })}}
                            {% endblock %}
                        {% endembed %}
                    {% endblock %}
                {% endembed %}

                {% embed 'management-neu/default/pzdetailsection_skeleton.html.twig' with { title: null } %}
                    {% block content %}
                        {% embed 'management-neu/default/pzdetailline_skeleton.html.twig' %}
                            {% block item1 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'Default Max. Tageskontingent', value: unloadingPoint.defaultDailyMaxContingent })}}
                            {% endblock %}
                            {% block item2 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'Default Max. Wochenkontingent', value: unloadingPoint.defaultWeeklyMaxContingent })}}
                            {% endblock %}
                        {% endembed %}
                    {% endblock %}
                {% endembed %}

                {% embed 'management-neu/default/pzdetailsection_skeleton.html.twig' with { title: null } %}
                    {% block content %}
                        {% embed 'management-neu/default/pzdetailline_skeleton.html.twig' %}
                            {% block item1 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'Abweichendes Kontingent Gültig von', value: unloadingPoint.deviantContingentValidFrom|date('d.m.Y') })}}
                            {% endblock %}
                            {% block item2 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'Abweichendes Kontingent Gültig bis', value: unloadingPoint.deviantContingentValidTo|date('d.m.Y') })}}
                            {% endblock %}
                        {% endembed %}
                    {% endblock %}
                {% endembed %}

                {% embed 'management-neu/default/pzdetailsection_skeleton.html.twig' with { title: null } %}
                    {% block content %}
                        {% embed 'management-neu/default/pzdetailline_skeleton.html.twig' %}
                            {% block item1 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'Abweichendes Max. Tageskontingent', value: unloadingPoint.deviantDailyMaxContingent })}}
                            {% endblock %}
                            {% block item2 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'Abweichendes Max. Wochenkontingent', value: unloadingPoint.deviantWeeklyMaxContingent })}}
                            {% endblock %}
                        {% endembed %}
                    {% endblock %}
                {% endembed %}

                <div class="mt-10 px-10">
                    <div class="flex flex-col lg:flex-row justify-between mb-5">
                        <div class="mb-5">
                            <h3 class="text-xl font-extralight mb-2">Vertragsgebiete</h3>
                            <p class="font-thin">Übersicht aller zugehörigen Vertragsgebiete der Anlage</p>
                        </div>
                    </div>
                </div>

                <div class="px-10 mb-5">
                    <div id="filters-table-unloadingPoint-contractArea"></div>
                    <table id="unloadingPoint-contractArea" class="blackgrid-contractArea theme-prezero"
                           data-source='{{ list|escape('html_attr') }}'
                           data-columns="{{ columns|json_encode|escape('html_attr') }}">
                    </table>
                </div>
            {% endblock %}
        {% endembed %}

        </div>
    </section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('management-unloadingpoint-details') }}
{% endblock %}
