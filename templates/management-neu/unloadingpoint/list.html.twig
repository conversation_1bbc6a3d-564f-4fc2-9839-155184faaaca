{% extends('management-neu/base.html.twig') %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('management-unloadingpoint-list') }}
{% endblock %}

{% block body %}
    <section class="container mx-auto px-4">
        <h1 class="font-thin text-lg mb-10 border-b-2 border-pzgreen">
            {{'unloading_point_management'|trans}}
        </h1>
        <div class="bg-white shadow-lg rounded-xl pb-1 mb-10 font-extralight">
            <div class="flex justify-between items-center bg-gradient-to-tr from-pzpetrol-dark via-pzpetrol-light to-pzblue-light px-10 py-5 shadow-lg rounded-t-xl mb-5 font-extralight">
                <h3 class="text-white font-thin text-xl">{{'management.unloading_points'|trans}}</h3>
            </div>
            <div class="px-10 mb-5">
                <h3 class="text-xl font-extralight mb-2">{{'management.unloading_points_overview'|trans}}</h3>
                <p class="font-thin"></p>
            </div>

            <div class="px-10 mb-5">
                <div id="filters-table-contractArea-list"></div>
                <table id="contractArea-list" class="blackgrid theme-prezero"
                       data-source='{{ list|escape('html_attr') }}'
                       data-columns="{{ columns|json_encode|escape('html_attr') }}">
                </table>
            </div>
        </div>
    </section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('management-unloadingpoint-list') }}
{% endblock %}
