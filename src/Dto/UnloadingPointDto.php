<?php

declare(strict_types=1);

namespace App\Dto;

use Symfony\Component\Serializer\Attribute as Serializer;

class UnloadingPointDto
{
    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $id;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $status;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $dsdId;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $esaId;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $name;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $street;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $houseNumber;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $postalCode;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $city;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $district;

    /**
     * @var string
     */
    #[Serializer\Groups(['list'])]
    public $detailLink;

    /**
     * @var bool
     */
    #[Serializer\Groups('list')]
    public $locked;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $defaultDailyMaxContingent;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $defaultWeeklyMaxContingent;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $deviantDailyMaxContingent;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $deviantWeeklyMaxContingent;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $deviantContingentValidFrom;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $deviantContingentValidTo;
}
