<?php

declare(strict_types=1);

namespace App\Services;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\ContractArea;
use App\Entity\Main\Order;
use App\Repository\Main\ContractRepository;
use App\Repository\Main\OrderRepository;

class OrderCollectCalculator
{
    public function __construct(
        private readonly \DateTime $start,
        private readonly \DateTime $end,
        private readonly OrderRepository $orderRepo,
        private readonly ContractRepository $contractRepo,
        private readonly UnloadingPointHelper $unloadingPointHelper,
    ) {
    }

    /**
     * @param array<mixed> $week
     */
    public function getMaximumOrderCount(
        ContractArea $area,
        CollectingPlace $collectingPlace,
        array $week,
        int $countOwnCollects,
        int $countOwnCancelleledCollects): int|float
    {
        $contract = $this->contractRepo->getActiveContractByCollectingPlace(contractArea: $area, collectingPlace: $collectingPlace);

        if (!is_null(value: $contract)) {
            $orderCountArea = $this->orderRepo->getOrderCountByArea(
                startDate: $week[0]->getDtObject(),
                endDate: $week[5]->getDtObject(),
                contractArea: $area
            );

            $validityOrderWeekCount = $this->unloadingPointHelper->getWeeklyMaxContingent(unloadingPoint: $contract->getUnloadingPoint(), date: $week[0]->getDtObject());

            // need to substract OWN cancelledCollects because they are not included in orderCountArea but already removed from weekly amount!
            $validityOrderWeekCount = $validityOrderWeekCount + $countOwnCollects - $countOwnCancelleledCollects - $orderCountArea;

            return $validityOrderWeekCount;
        } else {
            return 0;
        }
    }

    /**
     * @return array<Order>
     */
    public function getCollectOrders(CollectingPlace $collectingPlace, ContractArea $area): array
    {
        return $this->orderRepo->findInBetween(startDate: $this->start, endDate: $this->end, collectingPlace: $collectingPlace, contractArea: $area);
    }

    /**
     * @param array<Order> $orders
     *
     * @return array<Order>
     */
    public function getCancelledCollectOrders(array $orders): array
    {
        $cancelled = [];
        foreach ($orders as $order) {
            if ($order->getCanceled()) {
                $cancelled[] = $order;
            }
        }

        return $cancelled;
    }
}
