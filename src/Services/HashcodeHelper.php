<?php

declare(strict_types=1);

namespace App\Services;

use App\Entity\Main\User;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

class HashcodeHelper
{
    public function __construct(protected LoggerInterface $logger, protected EntityManagerInterface $objectManager)
    {
    }

    /**
     * @throws \Exception
     */
    public function generateHashCode(): string
    {
        return hash(algo: 'sha256', data: random_bytes(length: 10));
    }

    /**
     * @throws \Exception
     */
    public function setUserPasswordResetHash(User $user): User
    {
        $user->setPasswordResetHash(passwordResetHash: $this->generateHashCode());
        $this->objectManager->persist($user);
        $this->objectManager->flush();

        return $user;
    }
}
