<?php

declare(strict_types=1);

namespace App\Entity\Common;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

trait AddressTrait
{
    #[ORM\Column(type: Types::STRING, length: 60, nullable: false)]
    private string $street = '';

    #[ORM\Column(type: Types::STRING, length: 10, nullable: true)]
    private ?string $houseNumber = null;

    #[ORM\Column(type: Types::STRING, length: 10, nullable: false)]
    private string $postalCode = '';

    #[ORM\Column(type: Types::STRING, length: 60, nullable: false)]
    private string $city = '';

    #[ORM\Column(type: Types::STRING, length: 40, nullable: true)]
    private ?string $district = null;

    #[ORM\Column(type: Types::STRING, length: 3, nullable: false)]
    private string $country = '';

    public function getStreet(): ?string
    {
        return $this->street;
    }

    public function setStreet(?string $street): self
    {
        $this->street = $street;

        return $this;
    }

    public function getHouseNumber(): ?string
    {
        return $this->houseNumber;
    }

    public function setHouseNumber(?string $houseNumber): self
    {
        $this->houseNumber = $houseNumber;

        return $this;
    }

    public function getPostalCode(): ?string
    {
        return $this->postalCode;
    }

    public function setPostalCode(?string $postalCode): self
    {
        $this->postalCode = $postalCode;

        return $this;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(?string $city): self
    {
        $this->city = $city;

        return $this;
    }

    public function getDistrict(): ?string
    {
        return $this->district;
    }

    public function setDistrict(?string $district): self
    {
        $this->district = $district;

        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): self
    {
        $this->country = $country;

        return $this;
    }
}
