<?php

declare(strict_types=1);

namespace App\Entity\Main;

use App\Entity\Common\CommonTrait;
use App\Entity\Common\HasCreateModifyStamps;
use App\Entity\Common\HasUuid;
use App\Repository\Main\SystemProviderRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class SystemProvider (Systemgeber).
 */
#[ORM\Table(name: 'system_provider')]
#[ORM\Entity(repositoryClass: SystemProviderRepository::class)]
class SystemProvider implements HasUuid, HasCreateModifyStamps, \Stringable
{
    use CommonTrait;

    #[ORM\Column(name: 'material_id', type: Types::STRING, length: 40, nullable: false)]
    protected string $materialId;

    #[ORM\Column(name: 'name', type: Types::STRING, length: 255, nullable: false)]
    protected string $name;

    #[ORM\Column(name: 'system_provider_id', type: Types::STRING, length: 30, nullable: false)]
    protected string $systemProviderId;

    #[ORM\Column(name: 'long_text', type: Types::STRING, length: 255, nullable: true)]
    protected ?string $longtext = null;

    #[ORM\Column(name: 'dsd_fraction_id', type: Types::STRING, length: 30, nullable: false)]
    protected string $dsdFractionId;

    #[ORM\Column(name: 'dsd_fraction_name', type: Types::STRING, length: 30, nullable: false)]
    protected string $dsdFractionName;

    #[ORM\Column(name: 'transport', type: Types::BOOLEAN)]
    protected ?bool $transport = null;

    #[ORM\Column(name: 'mono_fraction', type: Types::BOOLEAN)]
    protected ?bool $monoFraction = null;

    /**
     * @var Collection<int, Contract>
     */
    #[ORM\ManyToMany(targetEntity: Contract::class, mappedBy: 'systemProviders')]
    private Collection $contracts;

    public function __construct()
    {
        $this->init();
        $this->contracts = new ArrayCollection();
    }

    public function getMaterialId(): string
    {
        return $this->materialId;
    }

    public function setMaterialId(string $materialId): SystemProvider
    {
        $this->materialId = $materialId;

        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): SystemProvider
    {
        $this->name = $name;

        return $this;
    }

    public function getSystemProviderId(): string
    {
        return $this->systemProviderId;
    }

    public function setSystemProviderId(string $systemProviderId): SystemProvider
    {
        $this->systemProviderId = $systemProviderId;

        return $this;
    }

    public function getLongtext(): ?string
    {
        return $this->longtext;
    }

    public function setLongtext(?string $longtext): SystemProvider
    {
        $this->longtext = $longtext;

        return $this;
    }

    public function getDsdFractionId(): string
    {
        return $this->dsdFractionId;
    }

    public function setDsdFractionId(string $dsdFractionId): SystemProvider
    {
        $this->dsdFractionId = $dsdFractionId;

        return $this;
    }

    public function getdsdFractionName(): string
    {
        return $this->dsdFractionName;
    }

    public function setdsdFractionName(string $dsdFractionName): SystemProvider
    {
        $this->dsdFractionName = $dsdFractionName;

        return $this;
    }

    public function getTransport(): bool
    {
        return $this->transport;
    }

    public function setTransport(bool $transport): SystemProvider
    {
        $this->transport = $transport;

        return $this;
    }

    public function getMonoFraction(): bool
    {
        return $this->monoFraction;
    }

    public function setMonoFraction(bool $monoFraction): SystemProvider
    {
        $this->monoFraction = $monoFraction;

        return $this;
    }

    /**
     * @return Collection<int, Contract>
     */
    public function getContracts(): Collection
    {
        return $this->contracts;
    }

    public function addContract(Contract $contract): self
    {
        if (!$this->contracts->contains(element: $contract)) {
            $this->contracts->add($contract);
            $contract->addSystemProvider(systemProvider: $this);
        }

        return $this;
    }

    public function removeContract(Contract $contract): self
    {
        if ($this->contracts->contains(element: $contract)) {
            $this->contracts->removeElement(element: $contract);
            $contract->removeSystemProvider(systemProvider: $this);
        }

        return $this;
    }

    public function __toString(): string
    {
        return $this->name;
    }
}
