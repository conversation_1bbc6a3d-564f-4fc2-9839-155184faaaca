<?php

declare(strict_types=1);

namespace App\Entity\Main;

use App\Entity\Common\AddressTrait;
use App\Entity\Common\CommonTrait;
use App\Entity\Common\HasCreateModifyStamps;
use App\Entity\Common\HasUuid;
use App\Repository\Main\UnloadingPointRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class UnloadingPoint (Entladestelle).
 */
#[ORM\Table(name: 'unloading_point')]
#[ORM\Entity(repositoryClass: UnloadingPointRepository::class)]
class UnloadingPoint implements HasUuid, HasCreateModifyStamps, \Stringable
{
    use CommonTrait;
    use AddressTrait;

    #[ORM\Column(name: 'unloading_point_id', type: Types::STRING, length: 255, nullable: false)]
    protected string $unloadingPointId = '';

    #[ORM\Column(name: 'dsd_id', type: Types::STRING, length: 255, nullable: false)]
    protected string $dsdId = '';

    #[ORM\Column(name: 'name_1', type: Types::STRING, length: 255, nullable: false)]
    protected string $name1 = '';

    #[ORM\Column(name: 'name_2', type: Types::STRING, length: 255, nullable: true)]
    protected ?string $name2 = null;

    #[ORM\ManyToOne(targetEntity: State::class, inversedBy: 'contractAreas')]
    #[ORM\JoinColumn(name: 'state', referencedColumnName: 'id', nullable: false)]
    protected ?State $state = null;

    /**
     * @var Collection<int, Contract>
     */
    #[ORM\OneToMany(targetEntity: Contract::class, mappedBy: 'unloadingPoint')]
    private Collection $contracts;

    /**
     * @var Collection<int, UnloadingPointValidity>
     */
    #[ORM\OneToMany(targetEntity: UnloadingPointValidity::class, mappedBy: 'unloadingPoint')]
    private Collection $unloadingPointValidities;

    #[ORM\Column(nullable: false)]
    private int $defaultDailyMaxContingent = 20;

    #[ORM\Column(nullable: false)]
    private int $defaultWeeklyMaxContingent = 100;

    #[ORM\Column(nullable: false)]
    private int $deviantDailyMaxContingent = 0;

    #[ORM\Column(nullable: false)]
    private int $deviantWeeklyMaxContingent = 0;

    #[ORM\Column(nullable: false)]
    private \DateTime $deviantContingentValidFrom;

    #[ORM\Column(nullable: false)]
    private \DateTime $deviantContingentValidTo;

    public function __construct()
    {
        $this->init();
        $this->contracts = new ArrayCollection();
        $this->unloadingPointValidities = new ArrayCollection();
        $this->deviantContingentValidFrom = new \DateTime('1970-01-01');
        $this->deviantContingentValidTo = new \DateTime('1970-01-01');
    }

    public function getUnloadingPointId(): string
    {
        return $this->unloadingPointId;
    }

    public function setUnloadingPointId(string $unloadingPointId): UnloadingPoint
    {
        $this->unloadingPointId = $unloadingPointId;

        return $this;
    }

    public function getDsdId(): string
    {
        return $this->dsdId;
    }

    public function setDsdId(string $dsdId): UnloadingPoint
    {
        $this->dsdId = $dsdId;

        return $this;
    }

    public function getName1(): string
    {
        return $this->name1;
    }

    public function setName1(string $name1): UnloadingPoint
    {
        $this->name1 = $name1;

        return $this;
    }

    public function getName2(): ?string
    {
        return $this->name2;
    }

    public function setName2(?string $name2): UnloadingPoint
    {
        $this->name2 = $name2;

        return $this;
    }

    public function getState(): State
    {
        return $this->state;
    }

    public function setState(?State $state): UnloadingPoint
    {
        $this->state = $state;

        return $this;
    }

    /**
     * @return Collection<int, Contract>
     */
    public function getContracts(): Collection
    {
        return $this->contracts;
    }

    public function addContract(Contract $contract): self
    {
        if (!$this->contracts->contains(element: $contract)) {
            $this->contracts->add(element: $contract);
            $contract->setUnloadingPoint(unloadingPoint: $this);
        }

        return $this;
    }

    public function removeContract(Contract $contract): self
    {
        if ($this->contracts->contains(element: $contract)) {
            $this->contracts->removeElement(element: $contract);
            // set the owning side to null (unless already changed)
            if ($contract->getUnloadingPoint() === $this) {
                $contract->setUnloadingPoint(unloadingPoint: null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, UnloadingPointValidity>
     */
    public function getUnloadingPointValidities(): Collection
    {
        return $this->unloadingPointValidities;
    }

    public function addUnloadingPointValidity(UnloadingPointValidity $unloadingPointValidity): self
    {
        if (!$this->unloadingPointValidities->contains(element: $unloadingPointValidity)) {
            $this->unloadingPointValidities->add(element: $unloadingPointValidity);
            $unloadingPointValidity->setUnloadingPoint(unloadingPoint: $this);
        }

        return $this;
    }

    public function removeUnloadingPointValidity(UnloadingPointValidity $unloadingPointValidity): self
    {
        if ($this->unloadingPointValidities->contains(element: $unloadingPointValidity)) {
            $this->unloadingPointValidities->removeElement(element: $unloadingPointValidity);
            // set the owning side to null (unless already changed)
            if ($unloadingPointValidity->getUnloadingPoint() === $this) {
                $unloadingPointValidity->setUnloadingPoint(unloadingPoint: null);
            }
        }

        return $this;
    }

    public function __toString(): string
    {
        return $this->dsdId.' - '.$this->name1.' ('.$this->city.')';
    }

    public function getDefaultDailyMaxContingent(): int
    {
        return $this->defaultDailyMaxContingent;
    }

    public function setDefaultDailyMaxContingent(int $defaultDailyMaxContingent): static
    {
        $this->defaultDailyMaxContingent = $defaultDailyMaxContingent;

        return $this;
    }

    public function getDefaultWeeklyMaxContingent(): int
    {
        return $this->defaultWeeklyMaxContingent;
    }

    public function setDefaultWeeklyMaxContingent(int $defaultWeeklyMaxContingent): static
    {
        $this->defaultWeeklyMaxContingent = $defaultWeeklyMaxContingent;

        return $this;
    }

    public function getDeviantDailyMaxContingent(): int
    {
        return $this->deviantDailyMaxContingent;
    }

    public function setDeviantDailyMaxContingent(int $deviantDailyMaxContingent): static
    {
        $this->deviantDailyMaxContingent = $deviantDailyMaxContingent;

        return $this;
    }

    public function getDeviantWeeklyMaxContingent(): int
    {
        return $this->deviantWeeklyMaxContingent;
    }

    public function setDeviantWeeklyMaxContingent(int $deviantWeeklyMaxContingent): static
    {
        $this->deviantWeeklyMaxContingent = $deviantWeeklyMaxContingent;

        return $this;
    }

    public function getDeviantContingentValidFrom(): \DateTime
    {
        return $this->deviantContingentValidFrom;
    }

    public function setDeviantContingentValidFrom(\DateTime $deviantContingentValidFrom): static
    {
        $this->deviantContingentValidFrom = $deviantContingentValidFrom;

        return $this;
    }

    public function getDeviantContingentValidTo(): \DateTime
    {
        return $this->deviantContingentValidTo;
    }

    public function setDeviantContingentValidTo(\DateTime $deviantContingentValidTo): static
    {
        $this->deviantContingentValidTo = $deviantContingentValidTo;

        return $this;
    }
}
