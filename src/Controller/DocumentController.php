<?php

declare(strict_types=1);

namespace App\Controller;

use App\Dto\DocumentDto;
use App\Entity\Main\Contract;
use App\Entity\Main\Document;
use App\Entity\Main\User;
use App\Form\Dto\DocumentDtoFormType;
use App\Repository\Main\ContractRepository;
use App\Repository\Main\DocumentRepository;
use App\Services\AccessHelper;
use App\Services\FileExtensionHelper;
use App\Services\FileStore;
use AutoMapperPlus\AutoMapperInterface;
use Doctrine\ORM\EntityManagerInterface;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class DefaultController.
 *
 * @extends BaseCrudController<Document>
 */
class DocumentController extends BaseCrudController
{
    private readonly EntityManagerInterface $manager;

    protected AccessHelper $accessHelper;

    /**
     * DefaultController constructor.
     */
    public function __construct(
        EntityManagerInterface $em,
        AccessHelper $accessHelper,
        SerializerInterface $serializer,
        private readonly AutoMapperInterface $autoMapper,
        DocumentRepository $repository,
        private readonly ContractRepository $contractRepository,
        private readonly TranslatorInterface $translator,
        private readonly FileStore $fileStore,
    ) {
        parent::__construct(repository: $repository, serializer: $serializer, entityManager: $em, accessHelper: $accessHelper, controllerName: 'DocumentController', listTwig: 'document/documents_view.html.twig', detailTwig: 'document/documents_view.html.twig');
        $this->manager = $em;
        $this->accessHelper = $accessHelper;
        $this->serializer = $serializer;
    }

    protected function mapEntityToDto(object $entity, Request $request): object
    {
        $self = $this;

        /** @phpstan-ignore-next-line */
        return $this->autoMapper->map($entity, DocumentDto::class, [
            'generateUrl' => fn (string $route, array $params): string => $self->generateUrl(route: $route, parameters: $params),
        ]);
    }

    protected function mapDtoToEntity(object $dto, object $entity, Request $request): object
    {
        /** @phpstan-ignore-next-line */
        return $this->autoMapper->mapToObject($dto, $entity, [
            'contractRepository' => $this->contractRepository,
        ]);
    }

    /**
     * @throws \DateMalformedStringException
     */
    protected function getListFindBy(Request $request): array
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            return [];
        }

        $contracts = [];
        $collectingPlaceList = $this->accessHelper->getCollectingPlaceList(user: $user);
        $filterDate = new \DateTime(datetime: 'now')->modify(modifier: '+1 month');

        foreach ($collectingPlaceList as $collectingPlace) {
            /** @var Contract $customerContracts */
            foreach ($collectingPlace->getContracts() as $customerContracts) {
                if ($customerContracts->getValidTo() < $filterDate) {
                    continue;
                }

                if ($this->accessHelper->checkUserAccessCombination(user: $user, contractArea: $customerContracts->getContractArea(), collectingPlace: $customerContracts->getCollectingPlace())) {
                    foreach ($customerContracts->getDocuments() as $customerDocuments) {
                        $contracts[] = $customerContracts;
                    }
                }
            }
        }

        return ['contract' => $contracts];
    }

    /**
     * @param array<int, object>   $entityList
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getListViewOptions(array $entityList, array $options): array
    {
        $options['columns'] = [
            'id' => ['visible' => false, 'label' => 'id'],
            'uuid' => ['visible' => false, 'label' => 'uuid'],
            'contractAreaDisplayText' => ['visible' => true, 'label' => $this->translator->trans('collectingplace.contract_area')],
            'date' => ['visible' => true, 'label' => $this->translator->trans('date')],
            'number' => ['visible' => true, 'label' => $this->translator->trans('weighing_slip_number')],
            'amount' => ['visible' => true, 'label' => $this->translator->trans('net_weight')],
            'unit' => ['visible' => true, 'label' => $this->translator->trans('unit')],
            'detailLink' => ['visible' => false],
        ];

        return $options;
    }

    #[Route(path: '/documents/list', name: 'app_default_documentsview', methods: ['GET'])]
    public function documentsView(Request $request): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_DOCUMENTS')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        return $this->processList(request: $request, newEntity: new Document(), formType: DocumentDtoFormType::class, redirectRoute: 'document', routeParams: []);
    }

    #[Route(path: '/file/{uuid}', name: 'documents_file', methods: ['GET'])]
    public function fileView(string $uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_DOCUMENTS')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException();
        }
        $collectingPlaces = $this->accessHelper->getCollectingPlaceList(user: $user);
        $document = $this->manager->getRepository(Document::class)->findOneBy(criteria: ['uuid' => Uuid::fromString(uuid: $uuid)]);

        if (!$document || !in_array(needle: $document->getContract()->getCollectingPlace(), haystack: $collectingPlaces)) {
            throw $this->createNotFoundException(message: 'File not found.');
        }

        $documentData = $document->getDocumentData();
        if (!$documentData || !$documentData->getS3FilePath()) {
            throw $this->createNotFoundException(message: 'File not found.');
        }

        $s3Path = $documentData->getS3FilePath();

        $response = new StreamedResponse(callbackOrChunks: function () use ($s3Path): void {
            $outputStream = fopen(filename: 'php://output', mode: 'wb');
            $fileStream = $this->fileStore->getStream(s3Path: $s3Path);

            if ($fileStream) {
                stream_copy_to_stream(from: $fileStream, to: $outputStream);
                fclose(stream: $fileStream);
            }
        });

        $response->headers->set(key: 'Content-Type', values: $documentData->getMimeType());

        $type = $document->getDocumentType()->getName();
        $name = $document->getNumber();
        $contract = $document->getContract()->getContractNumber();

        $fileExtensionHelper = new FileExtensionHelper();
        $fileExtension = $fileExtensionHelper->getFileExtensionByMimeType(mimeType: $documentData->getMimeType());

        $disposition = HeaderUtils::makeDisposition(
            disposition: HeaderUtils::DISPOSITION_ATTACHMENT,
            filename: sprintf('%s-%s-%s%s', $contract, $type, $name, $fileExtension)
        );

        $response->headers->set(key: 'Content-Disposition', values: $disposition);

        return $response;
    }
}
