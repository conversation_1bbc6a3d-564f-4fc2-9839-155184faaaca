<?php

declare(strict_types=1);

namespace App\Controller\Management;

use App\Dto\UserDto;
use App\Entity\Main\User;
use App\Form\Dto\UserDtoFormType;
use App\Form\GenericApplyFormType;
use App\Form\GenericDeleteFormType;
use App\Repository\Main\UserRepository;
use App\Services\AccessHelper;
use App\Services\CollectingPlaceHelper;
use App\Services\ContractAreaHelper;
use App\Services\Mailer;
use App\Services\UnloadingPointHelper;
use AutoMapperPlus\AutoMapperInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @extends BaseCrudController<User>
 */
class UserController extends BaseCrudController
{
    protected EntityManagerInterface $manager;
    protected SessionInterface $session;
    protected AccessHelper $accessHelper;

    public function __construct(
        EntityManagerInterface $em,
        RequestStack $requestStack,
        AccessHelper $accessHelper,
        ContractAreaHelper $contractAreaHelper,
        CollectingPlaceHelper $collectingPlaceHelper,
        UnloadingPointHelper $unloadingPointHelper,
        SerializerInterface $serializer,
        private readonly AutoMapperInterface $autoMapper,
        UserRepository $repository,
        protected Mailer $mailer,
        private readonly TranslatorInterface $translator,
    ) {
        parent::__construct(repository: $repository, serializer: $serializer, entityManager: $em, accessHelper: $accessHelper, contractAreaHelper: $contractAreaHelper, collectingPlaceHelper: $collectingPlaceHelper, unloadingPointHelper: $unloadingPointHelper, controllerName: 'UserController', listTwig: 'management-neu/user/list.html.twig', detailTwig: 'management-neu/user/details.html.twig');

        $this->session = $requestStack->getSession();
        $this->manager = $em;
        $this->accessHelper = $accessHelper;
        $this->contractAreaHelper = $contractAreaHelper;
        $this->collectingPlaceHelper = $collectingPlaceHelper;
        $this->serializer = $serializer;
    }

    /**
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getFormOptions(object $dto, string $mode, array $options): array
    {
        $roleChoices = [
            $this->translator->trans('roles.ROLE_ORDER') => 'ROLE_ORDER',
            $this->translator->trans('roles.ROLE_DOCUMENTS') => 'ROLE_DOCUMENTS',
        ];

        switch ($mode) {
            case BaseCrudController::FORM_MODE_ADD:
                asort(array: $roleChoices);
                $options = array_merge($options, ['mode' => BaseCrudController::FORM_MODE_ADD, 'method' => Request::METHOD_POST]);
                $options['roleChoices'] = $roleChoices;
                $options['roleAssignmentDisabled'] = !$this->isGranted(attribute: 'ROLE_MANAGER');

                break;
            case BaseCrudController::FORM_MODE_EDIT:
                if (1 === preg_match(pattern: '(@mail.schwarz|@prezero.com)', subject: $dto->email)) {
                    $roleChoices['PreZero - Manager'] = 'ROLE_MANAGER';
                    $roleChoices['PreZero - Auswertung'] = 'ROLE_REPORT';

                    if ($this->isGranted(attribute: 'ROLE_ADMIN')) {
                        $roleChoices['Administrator'] = 'ROLE_ADMIN';
                    }
                }

                asort(array: $roleChoices);
                $options = array_merge($options, ['mode' => BaseCrudController::FORM_MODE_EDIT, 'method' => Request::METHOD_PUT]);
                $options['roleChoices'] = $roleChoices;
                $options['roleAssignmentDisabled'] = !$this->isGranted(attribute: 'ROLE_MANAGER');

                break;
            case BaseCrudController::FORM_MODE_DELETE:
                $options = array_merge($options, ['mode' => BaseCrudController::FORM_MODE_DELETE, 'method' => Request::METHOD_DELETE]);

                break;
            case BaseCrudController::FORM_MODE_LOCK:
                $options = array_merge($options, ['action' => $this->generateUrl(route: 'management_user_lock', parameters: ['uuid' => $dto->id]), 'mode' => BaseCrudController::FORM_MODE_LOCK, 'method' => Request::METHOD_POST]);

                break;
            case BaseCrudController::FORM_MODE_RESET:
                $options = array_merge($options, ['action' => $this->generateUrl(route: 'management_user_reset', parameters: ['uuid' => $dto->id]), 'mode' => BaseCrudController::FORM_MODE_RESET, 'method' => Request::METHOD_POST]);

                break;
        }

        return $options;
    }

    protected function mapEntityToDto(object $entity, string|Request $request): object
    {
        $self = $this;

        /** @phpstan-ignore-next-line */
        return $this->autoMapper->map($entity, UserDto::class, [
            'generateUrl' => fn (string $route, array $params): string => $self->generateUrl(route: $route, parameters: $params),
        ]);
    }

    protected function mapDtoToEntity(object $dto, object $entity, Request $request): object
    {
        /** @phpstan-ignore-next-line */
        return $this->autoMapper->mapToObject($dto, $entity, []);
    }

    protected function getListViewOptions($entityList, $options): array
    {
        $options['columns'] = [
            'id' => ['visible' => false, 'label' => 'id'],
            'email' => ['visible' => true, 'label' => $this->translator->trans('email')],
            'locale' => ['visible' => $this->getParameter(name: 'app.language_switch_enabled'), 'label' => $this->translator->trans('locale')],
            'assignedRoles' => ['visible' => true, 'label' => $this->translator->trans('roles_title')],
            'status' => ['visible' => true, 'label' => $this->translator->trans('order.status')],
        ];

        return $options;
    }

    /**
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getDetailViewOptions(object $entity, array $options, Request $request): array
    {
        return $options;
    }

    /**
     * @param array<string, mixed> $routeParams
     */
    protected function processList(Request $request, object $newEntity, string $formType, string $redirectRoute, array $routeParams): Response
    {
        $self = $this;

        $newDto = $this->mapEntityToDto(entity: $newEntity, request: $request);

        $editForm = $this->createForm(type: $formType, data: $newDto, options: $this->getFormOptions(dto: $newDto, mode: BaseCrudController::FORM_MODE_ADD, options: []));

        $editForm->handleRequest($request);

        if ($editForm->isSubmitted() && $editForm->isValid()) {
            $newEntity = $this->mapDtoToEntity(dto: $newDto, entity: $newEntity, request: $request);

            $entity = $this->repository->findOneBy(criteria: ['email' => $newEntity->getEmail()]);

            if (null !== $entity) {
                if (!$entity->getDeleted()) {
                    $routeParams['noty'] = 'warning';
                    $routeParams['message'] = $routeParams['object'].' existiert bereits.';

                    return $this->redirectToRoute(route: $redirectRoute, parameters: $routeParams);
                }
                $newEntity = $entity;
                $newEntity->setDeleted(deleted: false);
                $newEntity->setResetPassword(resetPassword: true);
            }

            $this->manager->persist($newEntity);
            $this->entityManager->flush();

            $routeParams['noty'] = 'success';
            $routeParams['message'] = $routeParams['object'].' wurde erfolgreich angelegt.';

            if ($newEntity->getResetPassword()) {
                $this->mailer->sendPasswordReset(user: $newEntity, newUser: true);
            }

            return $this->redirectToRoute(route: $redirectRoute, parameters: $routeParams);
        }

        $items = $this->repository->findBy(criteria: $this->getListFindBy(request: $request), orderBy: $this->getListSorting(request: $request));

        $mappedList = array_map(callback: fn (object $entity): object => $self->mapEntityToDto(entity: $entity, request: $request), array: $items);

        $routeParams['object'] = null;

        return $this->render(view: $this->listTwig, parameters: $this->getListViewOptions(entityList: $items, options: [
            'controller_name' => $this->controllerName,
            'list' => $this->serializer->serialize($mappedList, 'json', ['groups' => ['list']]),
            'editForm' => $editForm->createView(),
        ]));
    }

    /**
     * @param array<string, mixed> $routeParams
     */
    protected function processReset(string $uuid, Request $request, string $redirectRoute, array $routeParams): Response
    {
        /** @var User $entity */
        $entity = $this->repository->findOneBy(criteria: ['uuid' => $uuid]);

        if (null == $entity) {
            throw $this->createNotFoundException();
        }

        if ($entity->getLocked()) {
            $routeParams['noty'] = 'alert';
            $routeParams['message'] = 'Benutzer ist gesperrt. Password zurücksetzen ist nicht möglich.';

            return $this->redirectToRoute(route: $redirectRoute, parameters: $routeParams);
        }

        $entity->setResetPassword(resetPassword: true);
        $this->entityManager->persist($entity);
        $this->entityManager->flush();

        if ($entity->getResetPassword()) {
            $this->mailer->sendPasswordReset(user: $entity);
        }

        $routeParams['noty'] = 'success';
        $routeParams['message'] = 'Passwort wurde erfolgreich zurückgesetzt. Mail wurde verschickt.';

        return $this->redirectToRoute(route: $redirectRoute, parameters: $routeParams);
    }

    #[Route(path: '/management/user', name: 'management_user')]
    public function managementContractArea(Request $request): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        return $this->processList(request: $request, newEntity: new User(), formType: UserDtoFormType::class, redirectRoute: 'management_user', routeParams: ['object' => 'Benutzer']);
    }

    #[Route(path: '/management/user/{uuid}', name: 'management_user_details', methods: ['GET', 'PUT'])]
    public function details(string $uuid, Request $request): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        return $this->processDetails(
            uuid: $uuid,
            request: $request,
            editFormType: UserDtoFormType::class,
            deleteFormType: GenericDeleteFormType::class,
            lockFormType: GenericApplyFormType::class,
            resetFormType: GenericApplyFormType::class,
            redirectRoute: 'management_user_details',
            routeParams: ['object' => 'Benutzer', 'uuid' => $uuid]);
    }

    #[Route(path: '/management/user/lock/{uuid}', name: 'management_user_lock', methods: ['POST'])]
    public function lock(string $uuid, Request $request): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        return $this->processLock(uuid: $uuid, request: $request, redirectRoute: 'management_user', routeParams: ['object' => 'Benutzer']);
    }

    #[Route(path: '/management/user/reset/{uuid}', name: 'management_user_reset', methods: ['POST'])]
    public function reset(string $uuid, Request $request): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        return $this->processReset(uuid: $uuid, request: $request, redirectRoute: 'management_user', routeParams: ['uuid' => $uuid]);
    }

    #[Route(path: '/management/user/{uuid}', name: 'management_user_delete', methods: ['DELETE'])]
    public function delete(string $uuid, Request $request): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        return $this->processDelete(uuid: $uuid, request: $request, redirectRoute: 'management_user', routeParams: ['object' => 'Benutzer']);
    }
}
