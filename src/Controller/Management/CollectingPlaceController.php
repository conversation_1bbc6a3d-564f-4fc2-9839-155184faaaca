<?php

declare(strict_types=1);

namespace App\Controller\Management;

use App\Dto\CollectingPlaceDto;
use App\Entity\Main\CollectingPlace;
use App\Form\Dto\CollectingPlaceDtoFormType;
use App\Form\GenericApplyFormType;
use App\Form\GenericDeleteFormType;
use App\Repository\Main\CollectingPlaceRepository;
use App\Services\AccessHelper;
use App\Services\CollectingPlaceHelper;
use App\Services\ContractAreaHelper;
use App\Services\Mailer;
use App\Services\UnloadingPointHelper;
use AutoMapperPlus\AutoMapperInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @extends BaseCrudController<CollectingPlace>
 */
class CollectingPlaceController extends BaseCrudController
{
    protected EntityManagerInterface $manager;
    protected SessionInterface $session;
    protected AccessHelper $accessHelper;

    public function __construct(
        EntityManagerInterface $em,
        RequestStack $requestStack,
        AccessHelper $accessHelper,
        ContractAreaHelper $contractAreaHelper,
        CollectingPlaceHelper $collectingPlaceHelper,
        UnloadingPointHelper $unloadingPointHelper,
        SerializerInterface $serializer,
        private readonly AutoMapperInterface $autoMapper,
        CollectingPlaceRepository $repository,
        protected Mailer $mailer,
        private readonly TranslatorInterface $translator,
    ) {
        parent::__construct(repository: $repository, serializer: $serializer, entityManager: $em, accessHelper: $accessHelper, contractAreaHelper: $contractAreaHelper, collectingPlaceHelper: $collectingPlaceHelper, unloadingPointHelper: $unloadingPointHelper, controllerName: 'CollectingPlaceController', listTwig: 'management-neu/collectingplace/list.html.twig', detailTwig: 'management-neu/collectingplace/details.html.twig');

        $this->session = $requestStack->getSession();
        $this->manager = $em;
        $this->accessHelper = $accessHelper;
        $this->contractAreaHelper = $contractAreaHelper;
        $this->collectingPlaceHelper = $collectingPlaceHelper;
        $this->serializer = $serializer;
    }

    /**
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getFormOptions(object $dto, string $mode, array $options): array
    {
        return match ($mode) {
            BaseCrudController::FORM_MODE_ADD => array_merge($options, ['mode' => BaseCrudController::FORM_MODE_ADD, 'method' => Request::METHOD_POST]),
            BaseCrudController::FORM_MODE_EDIT => array_merge($options, ['mode' => BaseCrudController::FORM_MODE_EDIT, 'method' => Request::METHOD_PUT]),
            BaseCrudController::FORM_MODE_DELETE => array_merge($options, ['mode' => BaseCrudController::FORM_MODE_DELETE, 'method' => Request::METHOD_DELETE]),
            BaseCrudController::FORM_MODE_LOCK => array_merge($options, ['action' => $this->generateUrl(route: 'management_collectingplace_lock', parameters: ['uuid' => $dto->id]), 'mode' => BaseCrudController::FORM_MODE_LOCK, 'method' => Request::METHOD_POST]),
            default => $options,
        };
    }

    protected function mapEntityToDto(object $entity, string|Request $request): object
    {
        $self = $this;

        /** @phpstan-ignore-next-line */
        return $this->autoMapper->map($entity, CollectingPlaceDto::class, [
            'generateUrl' => fn (string $route, array $params): string => $self->generateUrl(route: $route, parameters: $params),
        ]);
    }

    protected function mapDtoToEntity(object $dto, object $entity, Request $request): object
    {
        /** @phpstan-ignore-next-line */
        return $this->autoMapper->mapToObject($dto, $entity, []);
    }

    /**
     * @param array<int, object>   $entityList
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getListViewOptions(array $entityList, array $options): array
    {
        $options['columns'] = [
            'id' => ['visible' => false, 'label' => 'id'],
            'status' => ['visible' => true, 'label' => $this->translator->trans('order.status')],
            'dsdId' => ['visible' => true, 'label' => $this->translator->trans('collectingplace.field.dsdid')],
            'esaId' => ['visible' => true, 'label' => $this->translator->trans('collectingplace.field.esaid')],
            'name' => ['visible' => true, 'label' => $this->translator->trans('name')],
            'street' => ['visible' => true, 'label' => $this->translator->trans('street')],
            'houseNumber' => ['visible' => true, 'label' => $this->translator->trans('house_number')],
            'postalCode' => ['visible' => true, 'label' => $this->translator->trans('postal_code')],
            'city' => ['visible' => true, 'label' => $this->translator->trans('city')],
            'district' => ['visible' => true, 'label' => $this->translator->trans('district')],
            'detailLink' => ['visible' => false, 'label' => 'detailLink'],
        ];

        return $options;
    }

    protected function getDetailViewOptions(object $entity, array $options, Request $request): array
    {
        return $options;
    }

    /**
     * @param array<string, mixed> $routeParams
     */
    protected function processList(Request $request, object $newEntity, string $formType, string $redirectRoute, array $routeParams): Response
    {
        $self = $this;

        $newDto = $this->mapEntityToDto(entity: $newEntity, request: $request);

        $editForm = $this->createForm(type: $formType, data: $newDto, options: $this->getFormOptions(dto: $newDto, mode: BaseCrudController::FORM_MODE_ADD, options: []));

        $editForm->handleRequest($request);

        if ($editForm->isSubmitted() && $editForm->isValid()) {
            $newEntity = $this->mapDtoToEntity(dto: $newDto, entity: $newEntity, request: $request);

            $entity = $this->repository->findOneBy(criteria: ['email' => $newEntity->getEmail()]);

            if (null !== $entity) {
                $routeParams['noty'] = 'warning';
                $routeParams['message'] = $routeParams['object'].' existiert bereits.';

                return $this->redirectToRoute(route: $redirectRoute, parameters: $routeParams);
            }

            $this->entityManager->persist($newEntity);
            $this->entityManager->flush();

            $routeParams['noty'] = 'success';
            $routeParams['message'] = $routeParams['object'].' wurde erfolgreich angelegt.';

            if ($newEntity->getResetPassword()) {
                $this->mailer->sendPasswordReset(user: $newEntity, newUser: true);
            }

            return $this->redirectToRoute(route: $redirectRoute, parameters: $routeParams);
        }

        $items = $this->repository->findBy(criteria: $this->getListFindBy(request: $request), orderBy: $this->getListSorting(request: $request));

        $mappedList = array_map(callback: fn (object $entity): object => $self->mapEntityToDto(entity: $entity, request: $request), array: $items);

        $routeParams['object'] = null;

        return $this->render(view: $this->listTwig, parameters: $this->getListViewOptions(entityList: $items, options: [
            'controller_name' => $this->controllerName,
            'list' => $this->serializer->serialize($mappedList, 'json', ['groups' => ['list']]),
            'editForm' => $editForm->createView(),
        ]));
    }

    #[Route(path: '/management/collectingplace', name: 'management_collectingplace')]
    public function managementCollectingPlace(Request $request): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $self = $this;
        $items = $this->manager->getRepository(CollectingPlace::class)->findAll();

        $mappedList = array_map(callback: fn (CollectingPlace $entity): object => $self->mapEntityToDto(entity: $entity, request: $request), array: $items);

        return $this->render(view: 'management-neu/collectingplace/list.html.twig', parameters: $this->getListViewOptions(entityList: $items, options: [
            'backUrl' => $self->generateUrl(route: 'management_collectingplace'),
            'object' => 'Umschlag',
            'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_ASC), 'json', ['groups' => ['list']]),
        ]));
    }

    #[Route(path: '/management/collectingplace/{uuid}', name: 'management_collectingplace_details', methods: ['GET', 'PUT'])]
    public function details(string $uuid, Request $request): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        return $this->processDetails(
            uuid: $uuid,
            request: $request,
            editFormType: CollectingPlaceDtoFormType::class,
            deleteFormType: GenericDeleteFormType::class,
            lockFormType: GenericApplyFormType::class,
            resetFormType: GenericApplyFormType::class,
            redirectRoute: 'management_collectingplace_details',
            routeParams: ['object' => 'Umschlag', 'uuid' => $uuid]);
    }

    #[Route(path: '/management/collectingplace/lock/{uuid}', name: 'management_collectingplace_lock', methods: ['POST'])]
    public function lock(string $uuid, Request $request): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        return $this->processLock(uuid: $uuid, request: $request, redirectRoute: 'management_collectingplace', routeParams: ['object' => 'Umschlag']);
    }
}
