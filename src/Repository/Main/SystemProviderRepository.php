<?php

declare(strict_types=1);

namespace App\Repository\Main;

use App\Entity\Main\SystemProvider;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * Class SystemProviderRepository.
 *
 * @extends ServiceEntityRepository<SystemProvider>
 */
class SystemProviderRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct(registry: $registry, entityClass: SystemProvider::class);
    }
}
