<?php

declare(strict_types=1);

namespace App\Repository\Main;

use App\Entity\Main\CollectingPlace;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * Class CollectingPlaceRepository.
 *
 * @extends ServiceEntityRepository<CollectingPlace>
 */
class CollectingPlaceRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct(registry: $registry, entityClass: CollectingPlace::class);
    }

    public function checkDuplicateDSDId(string $dsdId): bool
    {
        $qb = $this->createQueryBuilder(alias: 'c')
            ->select('count(c.id)')
            ->andWhere('c.dsdId = :dsdId')
            ->andWhere('c.locked = :locked')
            ->setParameter(key: 'dsdId', value: $dsdId)
            ->setParameter(key: 'locked', value: false)
        ;

        $count = $qb->getQuery()->getSingleScalarResult();

        return $count > 0;
    }
}
