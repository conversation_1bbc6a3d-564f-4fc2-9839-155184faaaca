<?php

declare(strict_types=1);

namespace App\Repository\Main;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\Contract;
use App\Entity\Main\ContractArea;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * Class ContractRepository.
 *
 * @extends ServiceEntityRepository<Contract>
 */
class ContractRepository extends ServiceEntityRepository
{
    /**
     * ContractRepository constructor.
     */
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct(registry: $registry, entityClass: Contract::class);
    }

    public function getContractByData(ContractArea $contractArea, CollectingPlace $collectingPlace, mixed $systemProvider): mixed
    {
        $qb = $this->createQueryBuilder(alias: 'c')
            ->innerJoin(join: 'c.systemProviders', alias: 's')
            ->andWhere('c.contractArea = :contractArea')
            ->andWhere('c.collectingPlace = :collectingPlace')
            ->andWhere('s.id = :systemProvider');

        $qb->setParameter(key: 'contractArea', value: $contractArea)
            ->setParameter(key: 'collectingPlace', value: $collectingPlace)
            ->setParameter(key: 'systemProvider', value: $systemProvider);
        $qb->setMaxResults(maxResults: 1);

        return $qb->getQuery()->getOneOrNullResult();
    }

    public function getActiveContracts(ContractArea $contractArea): mixed
    {
        $qb = $this->createQueryBuilder(alias: 'c')
            ->andWhere('c.contractArea = :contractArea')
            ->andWhere('c.validTo >= :validTo');

        $qb->setParameter(key: 'contractArea', value: $contractArea)
            ->setParameter(key: 'validTo', value: new \DateTime());

        return $qb->getQuery()->getResult();
    }

    public function getActiveContractByCollectingPlace(ContractArea $contractArea, CollectingPlace $collectingPlace): mixed
    {
        $qb = $this->createQueryBuilder(alias: 'c')
            ->andWhere('c.contractArea = :contractArea')
            ->andWhere('c.collectingPlace = :collectingPlace')
            ->andWhere('c.validFrom <= :validFrom')
            ->andWhere('c.validTo >= :validTo')
        ;

        $qb->setParameter(key: 'contractArea', value: $contractArea)
            ->setParameter(key: 'collectingPlace', value: $collectingPlace)
            ->setParameter(key: 'validFrom', value: new \DateTime())
            ->setParameter(key: 'validTo', value: new \DateTime());

        $qb->setMaxResults(maxResults: 1);

        return $qb->getQuery()->getOneOrNullResult();
    }
}
