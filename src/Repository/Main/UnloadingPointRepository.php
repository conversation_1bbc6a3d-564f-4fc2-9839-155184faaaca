<?php

declare(strict_types=1);

namespace App\Repository\Main;

use App\Entity\Main\UnloadingPoint;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method UnloadingPoint|null find($id, $lockMode = null, $lockVersion = null)
 * @method UnloadingPoint|null findOneBy(array<string, mixed> $criteria, array<string, string>|null $orderBy = null)
 * @method UnloadingPoint[]    findAll()
 * @method UnloadingPoint[]    findBy(array<string, mixed> $criteria, array<string, string>|null $orderBy = null, $limit = null, $offset = null)
 *
 * @extends ServiceEntityRepository<UnloadingPoint>
 */
class UnloadingPointRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct(registry: $registry, entityClass: UnloadingPoint::class);
    }

    public function checkDuplicateDSDId(string $dsdId): bool
    {
        $qb = $this->createQueryBuilder(alias: 'c')
            ->select('count(c.id)')
            ->andWhere('c.dsdId = :dsdId')
            ->andWhere('c.locked = :locked')
            ->setParameter(key: 'dsdId', value: $dsdId)
            ->setParameter(key: 'locked', value: false)
        ;

        $count = $qb->getQuery()->getSingleScalarResult();

        return $count > 0;
    }
}
