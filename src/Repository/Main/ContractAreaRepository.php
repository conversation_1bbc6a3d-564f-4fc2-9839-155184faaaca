<?php

declare(strict_types=1);

namespace App\Repository\Main;

use App\Entity\Main\ContractArea;
use App\Entity\Main\UnloadingPoint;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * Class ContractAreaRepository.
 *
 * @extends ServiceEntityRepository<ContractArea>
 */
class ContractAreaRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct(registry: $registry, entityClass: ContractArea::class);
    }

    public function getContractAreaByUnloadingPoint(UnloadingPoint $unloadingPoint): mixed
    {
        $qb = $this->createQueryBuilder(alias: 'a')
            ->innerJoin(join: 'a.contracts', alias: 'c')
            ->where('c.unloadingPoint = :unloadingPoint');

        $qb->setParameter(key: 'unloadingPoint', value: $unloadingPoint);

        return $qb->getQuery()->getResult();
    }
}
