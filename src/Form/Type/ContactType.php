<?php

declare(strict_types=1);

namespace App\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Blank;
use Symfony\Component\Validator\Constraints\NotBlank;

/**
 * Class PasswordResetType.
 *
 * @psalm-suppress MissingTemplateParam
 */
class ContactType extends AbstractType
{
    /**
     * @throws \Exception
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('request_types', ChoiceType::class, [
                'choices' => $options['request_types'],
            ]
            )
            ->add('collecting_place', TextType::class)
            ->add('contract_area', TextType::class)
            ->add('disponumber', TextType::class)
            ->add('sender_email', EmailType::class)
            ->add('message', TextareaType::class)
            ->add('submit', SubmitType::class,
                ['label' => 'Send Message']
            );

        // security
        $builder->add('time', HiddenType::class, [
            'mapped' => false,
            'constraints' => [
                new NotBlank(),
            ],
        ]);
        $builder->add('email2', HiddenType::class, [
            'mapped' => false,
            'constraints' => [
                new Blank(),
            ],
        ]);
        $builder->add('telephone2', HiddenType::class, [
            'mapped' => false,
            'constraints' => [
                new Blank(),
            ],
        ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        parent::configureOptions(resolver: $resolver);
        $resolver->setDefaults(defaults: [
            'request_types' => [
                'Question about an Order' => 'order_question',
                // 'Request to Cancel' => 'cancel_request',
                'General Question' => 'general_request',
            ],
        ]);
    }
}
