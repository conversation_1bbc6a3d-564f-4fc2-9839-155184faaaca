<?php

declare(strict_types=1);

namespace App\Schedule;

use App\Message\CheckDSDNumber\CheckDSDNumber;
use App\Message\SendOpenCollectOrders\SendOpenCollectOrders;
use Symfony\Component\Scheduler\Attribute\AsSchedule;
use Symfony\Component\Scheduler\RecurringMessage;
use Symfony\Component\Scheduler\Schedule;
use Symfony\Component\Scheduler\ScheduleProviderInterface;

#[AsSchedule('default')]
class DefaultScheduleProvider implements ScheduleProviderInterface
{
    public function getSchedule(): Schedule
    {
        return new Schedule()
            ->add(message: RecurringMessage::cron(expression: '*/5 * * * *', message: new SendOpenCollectOrders()))
            ->add(message: RecurringMessage::cron(expression: '0 6 * * *', message: new CheckDSDNumber()))
        ;
    }
}
