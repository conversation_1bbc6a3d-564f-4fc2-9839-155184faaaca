<?php

declare(strict_types=1);

namespace App\Command;

use App\Repository\Main\CollectingPlaceRepository;
use App\Services\Mailer;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:checkDSDNumber',
    description: 'Check duplicates for dsd numbers.',
)]
class CheckDSDNumberCommand
{
    public function __construct(
        private readonly CollectingPlaceRepository $collectingPlaceRepo,
        private readonly Mailer $mailer,
    ) {
    }

    public function __invoke(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle(input: $input, output: $output);

        $duplicates = [];
        $collectingPlaces = $this->collectingPlaceRepo->findAll();
        foreach ($collectingPlaces as $collectingPlace) {
            $io->writeln(messages: 'Checking '.$collectingPlace->getDsdId());
            $result = $this->collectingPlaceRepo->checkDuplicateDSDId(dsdId: $collectingPlace->getDsdId());

            if ($result) {
                $duplicates[] = $collectingPlace->getDsdId();
            }
        }

        $duplicates = array_unique(array: $duplicates);

        $collectingPlaceDuplicates = [];
        foreach ($duplicates as $duplicate) {
            $collectingPlaceDuplicates[$duplicate] = $this->collectingPlaceRepo->findBy(criteria: ['dsdId' => $duplicate, 'locked' => false]);
        }

        $this->mailer->sendDuplicateMessages(
            collectingPlaceDuplicates: $collectingPlaceDuplicates,
        );

        $io->success(message: 'app:sendOpenCollectOrders');

        return 0;
    }
}
