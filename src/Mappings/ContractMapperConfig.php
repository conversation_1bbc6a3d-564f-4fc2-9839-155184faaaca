<?php

declare(strict_types=1);

namespace App\Mappings;

use App\Dto\ContractDto;
use App\Entity\Main\Contract;
use AutoMapperPlus\AutoMapperPlusBundle\AutoMapperConfiguratorInterface;
use AutoMapperPlus\Configuration\AutoMapperConfigInterface;
use AutoMapperPlus\MappingOperation\Operation;

class ContractMapperConfig implements AutoMapperConfiguratorInterface
{
    public function configure(AutoMapperConfigInterface $config): void
    {
        $config->registerMapping(Contract::class, ContractDto::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('id', fn ($entity) => $entity->getId())
            ->forMember('contractNumber', fn ($entity) => $entity->getContractNumber())
            ->forMember('validFrom', fn ($entity) => $entity->getValidFrom()->format('d.m.Y'))
            ->forMember('validTo', fn ($entity) => $entity->getValidTo()->format('d.m.Y'))
            ->forMember('collectingPlace', fn ($entity): string => $entity->getCollectingPlace()->getDsdId().' - '.$entity->getCollectingPlace()->getName1().' ('.$entity->getCollectingPlace()->getCity().')')
            ->forMember('unloadingPoint', fn ($entity): string => $entity->getUnloadingPoint()->getDsdId().' - '.$entity->getUnloadingPoint()->getName1().' ('.$entity->getUnloadingPoint()->getCity().')')
        ;

        $config->registerMapping(ContractDto::class, Contract::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('id', Operation::fromProperty(propertyName: 'id'))
            ->forMember('contractNumber', Operation::fromProperty(propertyName: 'contractNumber'));
    }
}
