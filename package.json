{"devDependencies": {"@babel/core": "7.28.5", "@babel/preset-env": "7.28.5", "@symfony/webpack-encore": "4.7.0", "@tailwindcss/forms": "0.5.10", "autoprefixer": "10.4.21", "core-js": "3.46.0", "file-loader": "6.2.0", "postcss": "8.5.6", "postcss-loader": "7.3.4", "regenerator-runtime": "0.14.1", "sass": "1.93.2", "tailwindcss": "3.4.18", "webpack-cli": "5.1.4", "webpack-notifier": "1.15.0"}, "license": "UNLICENSED", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress"}, "dependencies": {"@fortawesome/fontawesome-free": "6.7.2", "@popperjs/core": "2.11.8", "@prezero/blackgrid": "0.8.1", "bootstrap": "4.6.2", "copy-webpack-plugin": "12.0.2", "jquery": "3.7.1", "noty": "3.2.0-beta-deprecated", "postcss-import": "16.1.1", "sass-loader": "14.2.1", "webpack": "5.102.1"}}