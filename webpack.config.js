var Encore = require('@symfony/webpack-encore');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const path = require('path');

// Manually configure the runtime environment if not already configured yet by the "encore" command.
// It's useful when you use tools that rely on webpack.config.js file.
if (!Encore.isRuntimeEnvironmentConfigured()) {
    Encore.configureRuntimeEnvironment(process.env.NODE_ENV || 'dev');
}

Encore
    // directory where compiled assets will be stored
    .setOutputPath('public/build/')
    // public path used by the web server to access the output path
    .setPublicPath('/build')
    // only needed for CDN's or sub-directory deploy
    //.setManifestKeyPrefix('build/')

    /*
     * ENTRY CONFIG
     *
     * Add 1 entry for each "page" of your app
     * (including one that's included on every page - e.g. "app")
     *
     * Each entry will result in one JavaScript file (e.g. app.js)
     * and one CSS file (e.g. app.css) if your JavaScript imports CSS.
        */

    .addEntry('app', './assets/js/app.js')
    .addStyleEntry('main', './assets/scss/main.scss')
    .addStyleEntry('management', './assets/scss/management.scss')
    .addStyleEntry('tailwind', './assets/css/tailwind.css')
    
    .addEntry('ordernewclist', './assets/js/ordernewclist.js')
    .addEntry('ordernew', './assets/js/ordernew.js')
    .addEntry('orderoverview', './assets/js/orderoverview.js')
    .addEntry('contact', './assets/js/contact.js')
    .addEntry('documentlist', './assets/js/document-list.js')

    .addEntry('management-noty', './assets/js/management/noty.js')

    .addEntry('management-contractarea-list', './assets/js/management/contractarea-list.js')
    .addEntry('management-contractarea-details', './assets/js/management/contractarea-details.js')
    .addEntry('management-contractarea-permission', './assets/js/management/contractarea-permission.js')

    .addEntry('management-reporting', './assets/js/management/reporting.js')
    .addEntry('management-reporting-details', './assets/js/management/reporting-details.js')

    .addEntry('management-user-list', './assets/js/management/user-list.js')
    .addEntry('management-user-details', './assets/js/management/user-details.js')

    .addEntry('management-collectingplace-list', './assets/js/management/collectingplace-list.js')
    .addEntry('management-collectingplace-details', './assets/js/management/collectingplace-details.js')

    .addEntry('management-unloadingpoint-list', './assets/js/management/unloadingpoint-list.js')
    .addEntry('management-unloadingpoint-details', './assets/js/management/unloadingpoint-details.js')

    //.addEntry('page1', './assets/js/page1.js')
    //.addEntry('page2', './assets/js/page2.js')

    .enablePostCssLoader((options) => {
        options.postcssOptions = {
            // directory where the postcss.config.js file is stored
            path: './postcss.config.js'
        };
    })

    // When enabled, Webpack "splits" your files into smaller pieces for greater optimization.
    .splitEntryChunks()

    // will require an extra script tag for runtime.js
    // but, you probably want this, unless you're building a single-page app
    .enableSingleRuntimeChunk()

    // enables Sass/SCSS support
    .enableSassLoader()

    /*
     * FEATURE CONFIG
     *
     * Enable & configure other features below. For a full
     * list of features, see:
     * https://symfony.com/doc/current/frontend.html#adding-more-features
     */
    .cleanupOutputBeforeBuild()
    .enableBuildNotifications()
    .enableSourceMaps(!Encore.isProduction())
    // enables hashed filenames (e.g. app.abc123.css)
    .enableVersioning(Encore.isProduction())

    // enables @babel/preset-env polyfills
    .configureBabelPresetEnv((config) => {
        config.useBuiltIns = 'usage';
        config.corejs = 3;
    })

    // uncomment if you use TypeScript
    //.enableTypeScriptLoader()

    // uncomment to get integrity="..." attributes on your script & link tags
    // requires WebpackEncoreBundle 1.4 or higher
    //.enableIntegrityHashes(Encore.isProduction())

    // uncomment if you're having problems with a jQuery plugin
    .autoProvidejQuery()

    // uncomment if you use API Platform Admin (composer req api-admin)
    //.enableReactPreset()
    //.addEntry('admin', './assets/js/admin.js')

    //Copy static images
    .addPlugin(new CopyWebpackPlugin({
        patterns: [
            { from: './assets/img', to: 'img' },
        ]
    }))
    .copyFiles({
        from: './assets/i18n',
        to: 'i18n/[path][name].[ext]'
    })
    .copyFiles({
        from: './assets/icons',
        to: 'icons/[path][name].[ext]'
    })
    .copyFiles({
        from: './assets/svg',
        to: 'svg/[path][name].[ext]'
    })
    .copyFiles({
        from: './assets/fonts',
        to: 'fonts/[path][name].[ext]'
    })
;

module.exports = Encore.getWebpackConfig();
