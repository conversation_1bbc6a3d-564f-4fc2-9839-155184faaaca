<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20251104235605 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE unloading_point ADD default_daily_max_contingent INT DEFAULT NULL');
        $this->addSql('ALTER TABLE unloading_point ADD default_weekly_max_contingent INT DEFAULT NULL');
        $this->addSql('ALTER TABLE unloading_point ADD deviant_daily_max_contingent INT DEFAULT NULL');
        $this->addSql('ALTER TABLE unloading_point ADD deviant_weekly_max_contingent INT DEFAULT NULL');
        $this->addSql('ALTER TABLE unloading_point ADD deviant_contingent_valid_from TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('ALTER TABLE unloading_point ADD deviant_contingent_valid_to TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');

        // Set default values for existing data
        $this->addSql('UPDATE unloading_point SET default_daily_max_contingent = 20 WHERE default_daily_max_contingent IS NULL');
        $this->addSql('UPDATE unloading_point SET default_weekly_max_contingent = 100 WHERE default_weekly_max_contingent IS NULL');
        $this->addSql('UPDATE unloading_point SET deviant_daily_max_contingent = 0 WHERE deviant_daily_max_contingent IS NULL');
        $this->addSql('UPDATE unloading_point SET deviant_weekly_max_contingent = 0 WHERE deviant_weekly_max_contingent IS NULL');
        $this->addSql('UPDATE unloading_point SET deviant_contingent_valid_from = \'1970-01-01 00:00:00\' WHERE deviant_contingent_valid_from IS NULL');
        $this->addSql('UPDATE unloading_point SET deviant_contingent_valid_to = \'1970-01-01 00:00:00\' WHERE deviant_contingent_valid_to IS NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE unloading_point DROP default_daily_max_contingent');
        $this->addSql('ALTER TABLE unloading_point DROP default_weekly_max_contingent');
        $this->addSql('ALTER TABLE unloading_point DROP deviant_daily_max_contingent');
        $this->addSql('ALTER TABLE unloading_point DROP deviant_weekly_max_contingent');
        $this->addSql('ALTER TABLE unloading_point DROP deviant_contingent_valid_from');
        $this->addSql('ALTER TABLE unloading_point DROP deviant_contingent_valid_to');
    }
}
