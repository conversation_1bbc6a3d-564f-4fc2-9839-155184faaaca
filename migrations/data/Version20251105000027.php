<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20251105000027 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE unloading_point ALTER default_daily_max_contingent SET NOT NULL');
        $this->addSql('ALTER TABLE unloading_point ALTER default_weekly_max_contingent SET NOT NULL');
        $this->addSql('ALTER TABLE unloading_point ALTER deviant_daily_max_contingent SET NOT NULL');
        $this->addSql('ALTER TABLE unloading_point ALTER deviant_weekly_max_contingent SET NOT NULL');
        $this->addSql('ALTER TABLE unloading_point ALTER deviant_contingent_valid_from SET NOT NULL');
        $this->addSql('ALTER TABLE unloading_point ALTER deviant_contingent_valid_to SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE unloading_point ALTER default_daily_max_contingent DROP NOT NULL');
        $this->addSql('ALTER TABLE unloading_point ALTER default_weekly_max_contingent DROP NOT NULL');
        $this->addSql('ALTER TABLE unloading_point ALTER deviant_daily_max_contingent DROP NOT NULL');
        $this->addSql('ALTER TABLE unloading_point ALTER deviant_weekly_max_contingent DROP NOT NULL');
        $this->addSql('ALTER TABLE unloading_point ALTER deviant_contingent_valid_from DROP NOT NULL');
        $this->addSql('ALTER TABLE unloading_point ALTER deviant_contingent_valid_to DROP NOT NULL');
    }
}
