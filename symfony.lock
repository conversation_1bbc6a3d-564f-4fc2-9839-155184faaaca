{"codeception/codeception": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "5.0", "ref": "0d213956834c5652a34e4bf9456ef26119132a8a"}, "files": ["codeception.yml", "tests/Acceptance.suite.yml", "tests/Acceptance/.gitignore", "tests/Functional.suite.yml", "tests/Functional/.gitignore", "tests/Support/AcceptanceTester.php", "tests/Support/Data/.gitignore", "tests/Support/FunctionalTester.php", "tests/Support/Helper/.gitignore", "tests/Support/UnitTester.php", "tests/Support/_generated/.gitignore", "tests/Unit.suite.yml", "tests/Unit/.gitignore", "tests/_output/.gitignore"]}, "doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/doctrine-bundle": {"version": "2.18", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.13", "ref": "620b57f496f2e599a6015a9fa222c2ee0a32adcb"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "easycorp/easyadmin-bundle": {"version": "4.27", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.14", "ref": "13b3e524d1038a6861ad6c7fc64e31b644ba0c54"}, "files": ["config/routes/easyadmin.yaml"]}, "friendsofphp/php-cs-fixer": {"version": "3.89", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "friendsofsymfony/rest-bundle": {"version": "3.8", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "3762cc4e4f2d6faabeca5a151b41c8c791bd96e5"}}, "knplabs/knp-paginator-bundle": {"version": "v6.9.1"}, "liip/monitor-bundle": {"version": "2.24", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.6", "ref": "02c7cd93304dead890f9042fc2670dd9b0e0c091"}}, "mark-gerarts/automapper-plus-bundle": {"version": "1.5.0"}, "nelmio/api-doc-bundle": {"version": "4.38", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "c8e0c38e1a280ab9e37587a8fa32b251d5bc1c94"}}, "phpstan/phpstan": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5e490cc197fb6bb1ae22e5abbc531ddc633b6767"}}, "phpunit/phpunit": {"version": "12.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "11.1", "ref": "1117deb12541f35793eec9fff7494d7aa12283fc"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php", "bin/phpunit"]}, "ramsey/uuid-doctrine": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.3", "ref": "471aed0fbf5620b8d7f92b7a5ebbbf6c0945c27a"}}, "symfony/apache-pack": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5d454ec6cc4c700ed3d963f3803e1d427d9669fb"}}, "symfony/console": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["bin/console"]}, "symfony/debug-bundle": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["config/packages/debug.yaml"]}, "symfony/flex": {"version": "2.9", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "52e9754527a15e2b79d9a610f98185a1fe46622a"}, "files": [".env", ".env.dev"]}, "symfony/form": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "7d86a6723f4a623f59e2bf966b6aad2fc461d36b"}, "files": ["config/packages/csrf.yaml"]}, "symfony/framework-bundle": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.3", "ref": "5a1497d539f691b96afd45ae397ce5fe30beb4b9"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php", ".editorconfig"]}, "symfony/lock": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.2", "ref": "8e937ff2b4735d110af1770f242c1107fdab4c8e"}, "files": ["config/packages/lock.yaml"]}, "symfony/mailer": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "09051cfde49476e3c12cd3a0e44289ace1c75a4f"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.64", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "d8936e2e2230637ef97e5eecc0eea074eecae58b"}, "files": ["config/packages/messenger.yaml"]}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "f5f5f3e4c23f5349796b7de587f19c51e7104299"}, "files": ["config/packages/monolog.yaml"]}, "symfony/phpunit-bridge": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.3", "ref": "dc13fec96bd527bd399c3c01f0aab915c67fd544"}}, "symfony/property-info": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.3", "ref": "dae70df71978ae9226ae915ffd5fad817f5ca1f7"}, "files": ["config/packages/property_info.yaml"]}, "symfony/routing": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "ab1e60e2afd5c6f4a6795908f646e235f2564eb2"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/scheduler": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "caea3c928ee9e1b21288fd76aef36f16ea355515"}, "files": ["src/Schedule.php"]}, "symfony/security-bundle": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["config/packages/security.yaml", "config/routes/security.yaml"]}, "symfony/translation": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "620a1b84865ceb2ba304c8f8bf2a185fbf32a843"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/twig-bundle": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/uid": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "0df5844274d871b37fc3816c57a768ffc60a43a5"}}, "symfony/ux-twig-component": {"version": "2.31", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.13", "ref": "f367ae2a1faf01c503de2171f1ec22567febeead"}, "files": ["config/packages/twig_component.yaml"]}, "symfony/validator": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "8c1c4e28d26a124b0bb273f537ca8ce443472bfd"}, "files": ["config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.3", "ref": "a363460c1b0b4a4d0242f2ce1a843ca0f6ac9026"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "symfony/webpack-encore-bundle": {"version": "2.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.0", "ref": "719f6110345acb6495e496601fc1b4977d7102b3"}, "files": ["assets/app.js", "assets/styles/app.css", "config/packages/webpack_encore.yaml", "package.json", "webpack.config.js"]}, "twig/extra-bundle": {"version": "v3.22.0"}}