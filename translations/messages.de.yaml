Mon: Mo
Tue: Di
Wed: Mi
Thu: Do
Fri: Fr
Sat: Sa
January: Januar
February: Februar
March: März
April: April
May: Mai
June: Juni
July: Juli
August: August
September: September
October: Oktober
November: November
December: Dezember
Continue: <PERSON><PERSON>
Email could not be found.: Die E-Mail-Adresse konnte nicht gefunden werden.
You are logged in as: Sie sind angemeldet mit
Reset Password: Passwort zurück setzen
The two passwords are not equal. Please try again.: Die beiden Passwörter sind nicht gleich. Bitte versuchen Sie es noch einmal.
No User for the link has been found.: Es wurde kein Nutzer zum Link gefunden.
Save Password: Passwort speichern
Transfer: Übernehmen
System deliverer: Systemgeber
Disposition number: Dispositionsnr.
Driver message: Fahrerinfo
System generated: Systemseitig vergeben
This value should not be blank.: Der Wert darf nicht leer sein.
Display: Anzeigen
Save: Speichern
This e-mail address is already in use. You need to enter a different one.: Die E-Mail-Adresse existiert bereits, sie müssen eine andere E-Mail-Adresse eingeben.
Okay: Okay
The password is the same as before. Please type in a new one.: Das eingegebene Passwort ist identisch zum aktuellen Passwort, bitte geben sie ein neues Passwort ein.
Send Message: Nachricht senden
Auto generated mail - please do not reply to this mail: 'Dies ist eine automatisiert generierte E-Mail. Bitte antworten Sie nicht auf die E-Mail'
'Question about an Order' : 'Frage zum Auftrag'
'Request to Cancel' : 'Stornierungsanfrage'
'General Question' : 'Allgemeine Anfrage'

popup:
  cancel: Abbrechen
  save: Speichern
  delete: Löschen
  lock: Sperren
  unlock: Entsperren
  reset: Zurücksetzen

user: Benutzer
user.list: Benutzerliste
user.details: Benutzer
user.details.add: Benutzer anlegen
user.details.edit: Benutzer bearbeiten
user.details.delete: Benutzer löschen
user.details.lock: Benutzer sperren
user.details.unlock: Benutzer entsperren
user.details.reset: Passwort zurücksetzen
user.field.id: Id
user.field.fullname: Name
user.field.email: Email
user.field.roles: Zugeordnete Rollen
user.placeholder.fullname: Name eingeben
user.placeholder.email: Email eingeben
user.message.delete: Soll der Benutzer wirklich gelöscht werden?
user.message.lock: Soll der Benutzer wirklich gesperrt werden?
user.message.unlock: Soll der Benutzer wirklich entsperrt werden?
user.message.reset: Soll das Password des Benutzers wirklich zurückgesetzt werden? Der Benutzer erhält anschließend eine E-Mail.
user.myprofile: Mein Konto

collectingplace: Umschlag
collectingplace.list: Umschlagsliste
collectingplace.details: Umschlag
collectingplace.details.lock: Umschlag sperren
collectingplace.details.unlock: Umschlag entsperren
collectingplace.message.lock: Soll der Umschlag wirklich gesperrt werden?
collectingplace.message.unlock: Soll der Umschlag wirklich entsperrt werden?

collectingplace.field:
    dsdid: DSD-Nr.
    name: Name
    esaid: ESA-Nr.

unloadingPoint.field:
  dsdid: DSD-Nr.
  name: Name
  esaid: ESA-Nr.
  state: Bundesland

contractArea:
  field:
    name: Name
    state: Bundesland
    validFrom: Gültig von
    validTo: Gültig bis

roles:
  ROLE_ADMIN: 'PreZero - Administrator'
  ROLE_MANAGER: 'PreZero - Manager'
  ROLE_REPORT: 'PreZero - Auswertung'
  ROLE_ORDER: 'Erfasser'
  ROLE_DOCUMENTS: 'Wiegescheine'
  ROLE_USER: 'Benutzer'

exception.error.text.header: 'Es ist ein Fehler aufgetreten!'
exception.error.text.details: 'Es ist ein unerwarteter Fehler aufgetreten'
exception.error.text.try_later: 'Bitte versuchen Sie es zu einem späteren Zeitpunkt erneut'
exception.error.text.page_not_found: 'Die angeforderte Seite konnte leider nicht gefunden werden.'
exception.error.text.check_url: 'Überprüfen Sie die URL nach Fehlern oder kehren Sie zur Startseite zurück.'
contact.title: 'PreZero - Kontakt'
contact.header: 'Kontakt'
contact_form: 'Kontaktformular'
contact.request_type: 'Anfragetyp'
contact.collecting_place: 'Umschlag'
contact.contract_area: 'Vertragsgebiet'
contact.disponumber: 'Disponummer'
contact.sender_email: 'E-Mail-Adresse'
contact.company: 'Firma'
contact.message: 'Nachricht'
contact.message.send_success: 'Ihre Nachricht wurde erfolgreich versendet.'
document.title: 'PreZero - Dokumente'
contact.prezero: 'PreZero kontaktieren'
order.report.title: 'PreZero - Auftragsübersicht'
order.report: 'Auftragsübersicht'
order.report.evaluation: 'Auswertungen'
order.report.unloading_point: 'Entladestelle'
order.report.date_from: 'Datum von'
order.report.date_to: 'Datum bis'
order.report.export: 'Export'
collectingplace.contract_territory_management: 'Vertragsgebietsverwaltung'
collectingplace.assign_user: 'Benutzer zuweisen'
collectingplace.contract_area: 'Vertragsgebiet'
collectingplace.authorization: 'Benutzer zum Umschlag <b>%dsdId% - %name1% (%city%)</b> im Vertragsgebiet <b>%name%</b> berechtigen.'
collectingplace.collecting_place: 'Umschlag'
collectingplace.collecting_places: 'Umschläge'
collectingplace.contracts: 'Verträge'
collectingplace.quotas: 'Kontingente'
collectingplace.quotas.overview_text: 'Übersicht aller hinterlegten Kontingente des Vertragsgebiets.'
collectingplace.collecting_places.overview_text: 'Übersicht aller hinterlegten Umschläge im Vertragsgebiet.'
collectingplace.contracts.overview_text: 'Übersicht aller Verträge im Vertragsgebiet.'
collectingplace.contract_area_overview: 'Übersicht der Vertragsgebiete'
collectingplace.user_select_text: 'Benutzer auswählen, um diese dem ausgewählten Vertragsgebiet zuzuweisen.'
collectingplace.collecting_places_contract_area_text: 'Hier werden alle Umschläge zum Vertragsgebiet <br>%name%</b> angezeigt.'
index.login_header: 'PreZero - Login'
index.login: 'Login'
index.login_text: 'Melden Sie sich mit Ihren Zugangsdaten an:'
index.login_email: 'E-Mail:'
index.login_password: 'Passwort:'
index.login_password_reset: 'Passwort vergessen?'
index.login_submit: 'Anmelden'
index.support: 'Support'
index.to_the_contact_form: 'Zum Kontaktformular'
ordercalender.ordercount: '%count% von %countMax%'
ordercalender.disponumber: 'Disponr.:'
holiday.header: 'Feiertag'
go_to_overview: 'Zurück zur Übersicht'
go_to_homepage: 'Zur Startseite'
go_to_login_page: 'Zur Loginseite'
select.time_period: 'Bitte wählen Sie einen Zeitraum aus:'
calendar.week: 'Kalenderwoche'
calendar.week_abbreviation: 'KW'
to_the_portal: 'Zum Portal'
account: 'Account'
logged_in_as: 'Angemeldet als:'
old_management: 'Alte Verwaltung'
contract_area_management: 'Vertragsgebietsverwaltung'
collecting_places_management: 'Umschlagsverwaltung'
unloading_point_management: 'Anlagenverwaltung'
user_management: 'Benutzerverwaltung'
reporting: 'Auswertung'
logout: 'ausloggen'
save: 'Speichern'
delete: 'Löschen'
transfer: 'Übernehmen'
contact: 'Kontakt'
cancel_order: 'Stornieren'
cancel: 'Abbrechen'
ok: 'OK'
back_to_selection: 'Zurück zur Auswahl'
canceled: 'Storniert'
requested: 'Beauftragt'
created: 'Erfasst'
please_select: 'Bitte auswählen'
return: 'zurück'
privacy_policy: 'Datenschutzerklärung'
compliance: 'Compliance'
legal_notice: 'Impressum'
profile: 'Profil'
weighing_slip: 'Wiegescheine'
weighing_slip_number: 'Wiegescheinnummer'
commerce_and_industry: 'Gewerbe & Industrie'
private_households: 'Privathaushalte'
configure_disposal: 'Entsorgung konfigurieren'
date: 'Datum'
as_of: 'ab dem'
net_weight: 'Nettogewicht'
unit: 'Einheit'
actions: 'Aktionen'
count_collecting_place: 'Anzahl Umschläge'
count_user: 'Anzahl Benutzer'
count_contract_area: 'Anzahl Vertragsgebiete'
name: 'Name'
street: 'Straße'
house_number: 'Housenummer'
postal_code: 'Postleitzahl'
city: 'Stadt'
district: 'Ortsteil'
email: 'E-Mail_Adresse'
roles_title: 'Rollen'
sap_contractNumber: 'SAP-Vertragsnummer'
amount_week: 'Wochenkontingent'
amount_day: 'Tageskontingent'
order.index_header: 'PreZero - Index'
order.register_header: 'Auftrag anmelden'
order.collecting_place.select_question: 'Für welchen Umschlag möchten Sie einen Abholauftrag erstellen?'
order.collecting_place.select_text: 'Bitte wählen Sie Ihren Umschlag aus:'
order.contract_area.select_text: 'Bitte wählen Sie Ihr Vertragsgebiet aus:'
order.pickup.time_period_question: 'Für wann möchten Sie die Abholung beauftragen?'
order.collecting_place.infos: 'Infos zum Umschlag:'
order.collecting_place.no_infos_text: 'Keine Informationen zum Umschlag verfügbar!'
order.is_canceled: 'Diese Anfrage wurde storniert.'
order.driver_message: 'Fahrerinfo (max. 50 Zeichen)'
order.edit_is_not_possible: 'Eine Änderung ist nicht mehr möglich.<br> Bitte nutzen Sie das Kontaktformular.'
order.cancel_info: 'Da Ihre Stornierung erfolgreich übertragen wurde, nutzen Sie, bei weiteren Anliegen oder Rückfragen, bitte das Kontaktformular.'
order.process_cancellation: 'Stornierung ausführen?'
order.request_submit: 'Anfrage übertragen?'
order.thank_for_request: 'Vielen Dank für Ihre Anfrage!'
order.error_occurred: 'Leider ist ein Fehler für Ihre Anfrage aufgetreten!'
order.created: 'Auftrag angelegt'
order.transferred: 'Auftrag übertragen'
order.cancel: 'Auftrag storniert'
order.request_pickup: 'Abholung beauftragen'
order.date: 'Auftragsdatum'
order.status: 'Status'

order.cancel_popup: 'Wollen Sie Ihren <br>
                Auftrag <b><span id="popupCancelDispoNum"></span></b> (<span id="popupCancelSystemproviderName"></span>)
                <br>
                <br>
                für <b><span id="popupCancelDay"></span></b> den <b><span id="popupCancelDate"></span></b>
                <br>
                <br>
                für den Umschlag<br>
                <b>%dsdid% - %name1% (%city%)</b><br>
                <br>
                im Vertragsgebiet<br>
                <b>%areaName%</b><br/><br>
                <b><span id="popupCancelChargeable"></span></b> stornieren?<br>'

order.submit_popup: 'Wollen Sie Ihre Mengen ab dem<br>
                <b>%date%</b><br>
                <br>
                in der<br>
                <b>KW %calendarWeek%</b><br>
                <br>
                für den Umschlag<br>
                <b>%collectingPlace%</b><br>
                <br>
                im Vertragsgebiet<br>
                <b>%areaName%</b><br/><br>
                beauftragen?'

order.thanks_popup: 'Ihre Anfrage ab dem<br>
                <b>%date%</b><br>
                <br>
                in der<br>
                <b>KW %calendarWeek%</b><br>
                <br>
                für den Umschlag<br>
                <b>%collectingPlace%</b><br>
                <br>
                im Vertragsgebiet<br>
                <b>%areaName%</b><br/><br>
                wird bearbeitet.'

order.success_cancel: 'Ihre <b><span id="popupCancelSuccessChargeable"></span></b> Stornierung
                <br>
                 <br>
                für <b><span id="popupSuccessCancelDay"></span></b> den <b><span id="popupSuccessCancelDate"></span></b>
                <br>
                <br>
                für den Umschlag<br>
                 <b>%collectingPlace%</b><br>
                <br>
                im Vertragsgebiet<br>
                 <b>%areaName%</b><br/><br>
                wurde erfolgreich übermittelt.'
order.error_cancel: 'Bitte Ihre Anfrage für <br>
                <b><span id="popupErrorCancelDay"></span></b> den <b><span id="popupErrorCancelDate"></span></b><br>
                prüfen und ggf. die Stornierung erneut beauftragen. <br>
                Falls erneut Probleme auftreten nutzen Sie bitte das Kontaktformular.'

order.error: 'Bitte Ihre Anfragen für die<br>
                <b>KW %calendarWeek%</b><br>
                prüfen und ggf. die Abholung erneut beauftragen.'

management.collecting_places_management: 'Umschlagsverwaltung'
management.collecting_places: 'Umschläge'
management.collecting_places_overview: 'Übersicht der Umschläge'
management.reporting_by: 'Auswertung nach'
management.reporting: 'Auswertung'
management.contract_areas: 'Vertragsgebiete'
management.user_overview: 'Übersicht der Benutzer'
management.user_create: 'Benutzer anlegen'

management.unloading_points_management: 'Anlagenverwaltung'
management.unloading_points: 'Anlagen'
management.unloading_points_overview: 'Übersicht der Anlagen'

security.new_password_header: 'PreZero - Neues Passwort'
security.new_password_input_text: 'Geben Sie ihr neues Passwort ein.'
security.new_password_lable: 'Neues Passwort (Min. 8 Zeichen, eine Zahl, ein Groß- und Kleinbuchstabe und eines dieser Sonderzeichen: "@$.+_!#%&"):'
security.new_password_second_input: 'Neues Passwort, 2 Eingabe:'
security.password_saved_header: 'Passwort gespeichert'
security.password_saved_text: 'Ihr neues Passwort wurde gespeichert'
security.reset_password_header: 'PreZero Mengenmeldung - Passwort zurücksetzen'
security.enter_your_email: 'Geben Sie bitte Ihre E-Mail ein,<br>mit der Sie sich registriert haben.'
security.link_sent: 'Link versendet!'
security.link_sent_text: 'Falls uns die angegebene E-Mail-Adresse bekannt ist, wurde der Link zum <br /> Zurücksetzen Ihres Passwortes an dieselbe E-Mail-Adresse versendet!'

user.profile_header: 'PreZero - Mein Profil'
user.profile_title: 'Benutzerprofil'
user.change_password: 'Passwort ändern'
user.change_password_text: 'Klicken sie auf "Okay" wenn sie eine E-Mail mit einem Link für das Setzen eines neuen Passwortes erhalten möchten.'
user.change_email: 'E-Mail ändern'
user.change_email_text: 'Geben Sie ihre neue E-Mail Adresse und ihr aktuelles Passwort ein.'
user.new_email: 'Neue E-Mail-Adresse'
user.new_email_saved_header: 'E-Mail-Adresse gespeichert'
user.new_email_saved_text: 'Die neue E-Mail-Adresse wurde gespeichert.'
user.current_password: 'Aktuelles Passwort'
user.master_data: 'Stammdaten'
user.company: 'Firma'
user.street_and_house_number: 'Straße / Nr.'
user.postal_code_and_city: 'PLZ / Ort'
user.reset_password_link_sent: 'Passwort Reset Link versendet!'
user.reset_password_link_sent_text: 'Der Link wurde an %email% versendet.'

email.contact_details: 'Kontaktdaten'
email.set_password_header: 'PreZero Mengenmeldung - Neuer Zugang'
email.reset_password_header: 'PreZero Mengenmeldung - Passwort zurücksetzen'
email.greeting: 'Guten Tag,'
email.set_password_link_text: 'mit folgendem Link können Sie Ihr Passwort zu Ihrem neuen Benutzerprofil setzen:'
email.reset_password_link_text: 'mit folgendem Link können Sie Ihr Passwort zurücksetzen:'
email.warning_text: 'Falls Sie kein neues Passwort angefordert haben, wenden Sie sich bitte an uns:'
email.thanks_for_request: 'vielen Dank für Ihre Anfrage.'
email.cancel_confirm_text: 'Hiermit bestätigen wir die Stornierung von folgender Position:'
system_provider: 'Systemgeber'
email.order_cancel_time_limit_text: 'Da der Abholtermin in 48 Stunden oder weniger terminiert ist, müssen wir Ihnen die Stornierung leider <b>kostenpflichtig</b> berechnen.'
email.order_cancel_contact_form_text: 'Bei Fragen oder Änderungen kontaktieren Sie uns bitte über das'
email.order_cancel_email_text: 'oder alternativ über folgende E-Mail-Adresse'
email.time_period: 'Zeitraum'
email.order_positions_confirm_text: 'Hiermit bestätigen wir den Eingang folgender Positionen:'
email.order_position_confirm_text: 'Hiermit bestätigen wir den Eingang folgender Position:'
locale.en: 'Englisch'
locale.de: 'Deutsch'
locale: 'Sprache'
user.change_locale: 'Sprache ändern'
contract_area_validity: 'Vertragsgebietsgültigkeit'
contract_areas_validity: 'Vertragsgebietsgültigkeiten'
quantity_per_day: 'Menge pro Tag'
quantity_per_week: 'Menge pro Woche'
total_per_week: 'Gesamt pro Woche'
from: 'von'
contract: 'Vertrage'
contracts: 'Verträge'
to_management: 'Zur Verwaltung'
to_reporting: 'Zur Auswertung'
prezero_management: 'PreZero Verwaltung'
management: 'Verwaltung'
overview: 'Übersicht'
data: 'Daten'
order: 'Bestellung'
orders: 'Bestellungen'
interface: 'Schnittstelle'
system_data: 'Systemdaten'
states: 'Bundesländer'
state: 'Bundesland'
customer_view: 'Kundenansicht'
to_customer_view: 'Zur Kundenansicht'
transfer_date: 'Übertragungsdatum'
transfer_status: 'Übertragungsstatus'
overdraft_info: 'Dispoinfo'
abbreviation: 'Kürzel'
system_provider_id: 'Systemgeber Id'
unloading_point : 'Entladestelle'
unloading_points : 'Entladestellen'
users: 'Benutzer'
reset_password: 'Passwort zurücksetzen'
customer: 'Kunde'
manager: 'Manager'
dashboard.overview_text: 'Diese Seite ist ein Kurzüberblick des Verwaltungsbereichs.<br> Oben links mit dem Link "<i class="fa fa-arrow-circle-left"></i>&nbsp;Zur Auswertung" gelangt man auf die Übersicht zur Auswertung aller Bestellungen zu den Entladestellen.'
dashboard.data_text: 'Unter dem Bereich <u>Daten</u> können/müssen Daten bearbeitet werden.'
dashboard.customer_text: 'Das Objekt Kunde bildet im Portal eine Hülle und bündelt eine/mehrere Umschlag/Umschläge und Benutzer.'
dashboard.collecting_place_text: 'Die hier angegebenen Umschläge werden aus dem SAP übertragen, weswegen keine Änderung in den Stammdaten möglich ist.<br> An dieser Stelle ist es möglich die Umschläge zu den Kunden zuzuordnen.'
dashboard.user_text: 'An dieser Stelle können Benutzer angelegt werden. Sowohl Verwaltungs-Benutzer (PreZero), als auch die Benutzer für die Kunden.<br> Außerdem können an dieser Stelle die Benutzer den Kunden zugeordnet werden. Bei der Anlage muss eine Rolle angegeben werden:'
dashboard.user_roles_text: 'Die Rolle <u>Kunde</u> muss dem Benutzer des Kunden zugeordnet werden. Zusätzlich benötigt der Benutzer eine Zuordnung zu einem Kunden, ansonsten würde der Benutzer einen Fehler bekommen/keine Daten sehen.<br> Die Rolle <u>Manager</u> muss einem Benutzer von PreZero/Schwarz-Gruppe zugeordnet werden. Mit dieser Rolle kommt man in den Verwaltungsbereich und kann die Auswertung durchführen. Die Rolle <u>Manager</u> kann man nur @prezero.com und @mail.schwarz E-Mail-Adressen zuordnen. Bei anderen E-Mail-Adressen kommt ein Fehler.'
dashboard.user_reset_password_text: 'Wenn ein neuer Benutzer angelegt oder "Passwort zurücksetzen" ausgewählt wird, wird automatisch, mit dem anlegen/speichern ein Passwort-Zurücksetzen-Link zu der angegebenen E-Mail-Adresse des Benutzers verschickt. Es ist daher <u>nicht</u> möglich ein eigenes Passwort im Verwaltungsbereich zusetzen.<br> Jeder angemeldete Benutzer kann anschließend unter "Mein Profil" das Passwort ändern.'
dashboard.order_text: 'Unter dem Bereich <u>Bestellungen</u> werden die Bestellungen des Kunden angezeigt.'
dashboard.order_status_text: 'Alle Bestellungen haben ein Status (Übertragen, Fehler, kein Status). Bestellungen können den "Fehler"-Status inne haben, wenn z. B. das SAP-System nicht erreichbar war. Hier gibt es darüber hinaus die Möglichkeit zu den nicht übertragenen Bestellungen die Entladestelle und das Auftragsdatum zu verändern. Fehlerhaft übertragene Bestellungen werden beim Speichern erneut an das SAP-System übertragen.'
dashboard.interface_text: 'Unter dem Bereich <u>Schnittstelle</u> werden die Daten angezeigt, welche aus dem SAP-System in das Portal übertragen wurden. In diesem Bereich können keine Daten verändert werden, da das SAP-System das führende System ist. Bei manchen Objekte (wie z. B. Verträge) kann man in die Detail-Ansicht springen, um weitere Informationen zu sehen.'
dashboard.contract_areas_text: 'Hier werden alle jemals übertragenen Vertragsgebiete angezeigt.'
dashboard.contracts_text: 'Hier können alle übertragenen Verträge zu den Vertragsgebieten angesehen werden. Außerdem sind hier die Informationen zur Lade- und Entladestellen zu finden. Darüber hinaus kann hier eingesehen werden, wie viele und welche Systemgeber zur <u>aktuellen Zeit</u> zu dem Vertrag aktiv sind.'
dashboard.unloading_points_text: 'Hier werden alle jemals übertragenen Entladestellen angezeigt.'
dashboard.system_provider_text: 'Hier werden alle jemals übertragenen Systemgeber angezeigt.'
dashboard.system_data_text: 'Unter dem Bereich <u>Systemdaten</u> werden die Daten angezeigt, welche dauerhaft im System vorhanden sind und sein müssen.'
dashboard.states_text: 'Hier werden alle Bundesländer mit dem offiziellen Landeskürzel angezeigt. Anhand dessen werden die Feiertage berechnet.'
dashboard.customer_view_text: 'Über "Zur Kundenansicht" kann man sich als Kunde anmelden, sofern man einem zugewiesen ist. Diese Funktion sollte nur zum Test und Fehlersuche genutzt werden.'
