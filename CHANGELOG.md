# Changelog

## [3.1.0](https://github.com/prezero/lvpportal-sf/compare/v3.0.0...v3.1.0) (2025-11-04)


### Features

* **LVP-13:** hide invalid contracts ([bca47bf](https://github.com/prezero/lvpportal-sf/commit/bca47bfd365a4ebbc772d1b409941ccd174015e9))
* **LVP-13:** hide invalid contracts ([840b0c1](https://github.com/prezero/lvpportal-sf/commit/840b0c1449bc00652ae3a197ca6b24cc4df77b81))


### Bug Fixes

* error ([3a17344](https://github.com/prezero/lvpportal-sf/commit/3a173447c4076fd17bb279b0f35619bc5c126060))


### Miscellaneous Chores

* install captainhook ([513bee7](https://github.com/prezero/lvpportal-sf/commit/513bee7d37759cf650e2093b2e42f7edafa6766e))
* install captainhook ([ab58fb9](https://github.com/prezero/lvpportal-sf/commit/ab58fb9d3ee2c9a6d521b2d2c97d8ca1bfe0cba6))
* install captainhook ([b7a1a54](https://github.com/prezero/lvpportal-sf/commit/b7a1a54d24aee8c13b5544a57fa07f29b61828ca))

## [3.0.0](https://github.com/prezero/lvpportal-sf/compare/v2.0.3...v3.0.0) (2025-11-04)


### Features

* add monitoring ([a508663](https://github.com/prezero/lvpportal-sf/commit/a508663c8ea4b39d1d6fd906bde519a4d347c0a9))
* Add S3 service and command to migrate file storage ([#109](https://github.com/prezero/lvpportal-sf/issues/109)) ([4ce8e14](https://github.com/prezero/lvpportal-sf/commit/4ce8e14125120dc4ce3de4ef5772d98722c12b89))
* **LVP-10:** composer vulnerabilities fixes ([#84](https://github.com/prezero/lvpportal-sf/issues/84)) ([#96](https://github.com/prezero/lvpportal-sf/issues/96)) ([cde7b22](https://github.com/prezero/lvpportal-sf/commit/cde7b22049efc9b007a1fbdbcc370f68f6ef7b0c))
* phpstan ([cb93cbf](https://github.com/prezero/lvpportal-sf/commit/cb93cbfa8af1a07ea6db507fe4b6ef0ccf4be423))
* replace psalm with phpstan ([#99](https://github.com/prezero/lvpportal-sf/issues/99)) ([0f2777d](https://github.com/prezero/lvpportal-sf/commit/0f2777d86636c6b27e1ad1fb3aa4f55cec099276))
* symfony 7.3 upgrade ([#92](https://github.com/prezero/lvpportal-sf/issues/92)) ([c0195d0](https://github.com/prezero/lvpportal-sf/commit/c0195d07e421e1e31a9c7b35c528c175c7a70868))
* update project ([85798a7](https://github.com/prezero/lvpportal-sf/commit/85798a7cef8fd7207c35bb54e4228c1b78eb935c))


### Bug Fixes

* add health-check firewall entry ([8dff6fb](https://github.com/prezero/lvpportal-sf/commit/8dff6fbb2a7967c219ce30a92df141b935ca6c1e))
* add health-check routes ([becd76a](https://github.com/prezero/lvpportal-sf/commit/becd76a8cb9e3ceb391d89156324d7cb196f9a51))
* **deps:** pin dependencies ([#102](https://github.com/prezero/lvpportal-sf/issues/102)) ([afbd7fe](https://github.com/prezero/lvpportal-sf/commit/afbd7feaf861c15dbdab89930fceb8bc12e3e43b))
* **deps:** pin dependency @fortawesome/fontawesome-free to 6.7.2 ([#103](https://github.com/prezero/lvpportal-sf/issues/103)) ([ef2ea94](https://github.com/prezero/lvpportal-sf/commit/ef2ea94d20bce31527ac7ea58c5f1e41981a1c83))
* **deps:** update npm packages to major versions ([#108](https://github.com/prezero/lvpportal-sf/issues/108)) ([d7cb518](https://github.com/prezero/lvpportal-sf/commit/d7cb518511840df12a17906e8652243fb2369419))
* env ([fe94543](https://github.com/prezero/lvpportal-sf/commit/fe94543ae1fb23f566900a428510a6e73cc47537))
* packages ([2a5b76d](https://github.com/prezero/lvpportal-sf/commit/2a5b76d484466f701fea7d5343c6962b4c940bfa))
* phpstan lvl 1 ([4e8218c](https://github.com/prezero/lvpportal-sf/commit/4e8218c8260ab5aa1a7237925d378fb95cb672b9))
* phpstan lvl 4 ([0fd9818](https://github.com/prezero/lvpportal-sf/commit/0fd9818a6d9473c31ff1f1f740f8d52e5305c1ca))
* phpstan lvl 5 ([8e35b3f](https://github.com/prezero/lvpportal-sf/commit/8e35b3f8bbb059acda99168ca91f74fa60fb302f))
* phpstan lvl 5 ([dd5d1a2](https://github.com/prezero/lvpportal-sf/commit/dd5d1a2bd52b1db0b614b4adf4cd18150035853b))
* phpstan lvl 5 ([217dd27](https://github.com/prezero/lvpportal-sf/commit/217dd27bca97db6a11a4b1980cda80d99ac4f083))
* phpstan lvl 6 ([ca1058a](https://github.com/prezero/lvpportal-sf/commit/ca1058a75d26f518d4f0cb1e5414fa1ade7db27d))
* remove leftover mariadb migration script ([2e86ce7](https://github.com/prezero/lvpportal-sf/commit/2e86ce77aef0870687c1743d8e647eb62e3567cf))
* security issues ([849f64d](https://github.com/prezero/lvpportal-sf/commit/849f64d2fa453015c82352d0eb1ef46bb8245c44))
* yarn file ([d03a63b](https://github.com/prezero/lvpportal-sf/commit/d03a63bae29d6194facfb99c6d941af3979523d8))


### Miscellaneous Chores

* **deps:** pin dependencies ([#101](https://github.com/prezero/lvpportal-sf/issues/101)) ([9b59651](https://github.com/prezero/lvpportal-sf/commit/9b59651ee2d3b1c84d7224b8d9e7bfbabaea42d3))
* **deps:** update dependency sass to v1.93.3 ([#110](https://github.com/prezero/lvpportal-sf/issues/110)) ([d648c14](https://github.com/prezero/lvpportal-sf/commit/d648c14025a80c23376b92be5d8a9079efa982b4))
* **deps:** update ghcr.io/prezero/docker-images/franken docker tag to v1.1.8 ([#111](https://github.com/prezero/lvpportal-sf/issues/111)) ([a6b15f3](https://github.com/prezero/lvpportal-sf/commit/a6b15f3e942b15e87350f56524ceb132041f5eba))
* **deps:** update github actions ([#104](https://github.com/prezero/lvpportal-sf/issues/104)) ([cd3e61d](https://github.com/prezero/lvpportal-sf/commit/cd3e61d5a24497407934d51bfb2f104782b2a814))
* **deps:** update mariadb docker tag to v10.11.14 ([#105](https://github.com/prezero/lvpportal-sf/issues/105)) ([3915235](https://github.com/prezero/lvpportal-sf/commit/3915235c9f2ad17cbf6d1eab9ebc73fbe6119c50))
* **deps:** update prezero/workflows action to v1.40.5 ([#112](https://github.com/prezero/lvpportal-sf/issues/112)) ([d269c92](https://github.com/prezero/lvpportal-sf/commit/d269c92c6403f2d5ffc34683db5f449abe1537cb))
* release 3.0.0 ([d68864e](https://github.com/prezero/lvpportal-sf/commit/d68864e9da9240c186abc0a6c954358f7c2f4024))
* update packages ([ca121c4](https://github.com/prezero/lvpportal-sf/commit/ca121c46c27dd2433e82575e9ddb2a4bdab80928))
* update packages ([be26121](https://github.com/prezero/lvpportal-sf/commit/be261210d6b5a4b3d638bc9784698997b4553c5b))
* update packages ([b400e88](https://github.com/prezero/lvpportal-sf/commit/b400e8842527d0e391594ed56e2a12c182d6aaa3))
* update packages ([85b7b81](https://github.com/prezero/lvpportal-sf/commit/85b7b811d8a5958bd4054d5cc71b32c27a576d12))
* update packages ([cc44bfd](https://github.com/prezero/lvpportal-sf/commit/cc44bfd628b555b05a4540c98a979cc3bf9c33be))
* update packages ([8e3eda3](https://github.com/prezero/lvpportal-sf/commit/8e3eda3600ad7dc18fd9f115da1adec17f61befe))


### Code Refactoring

* **db:** migrate from MariaDB to PostgreSQL Symfony 7.3 base ([#98](https://github.com/prezero/lvpportal-sf/issues/98)) ([424b217](https://github.com/prezero/lvpportal-sf/commit/424b21709485dc3b9f8c4b972989ddbb6231b436))


### Continuous Integration

* add sonarqube snyk and k8s workflows  ([#97](https://github.com/prezero/lvpportal-sf/issues/97)) ([8341f11](https://github.com/prezero/lvpportal-sf/commit/8341f11023b4f9be4a1e7ae5d781a407c69d5f5e))
* change dockerfile for k8s ([3715d2c](https://github.com/prezero/lvpportal-sf/commit/3715d2c24d511995d036226bdf68b37b92e5aff8))
* change dockerfile for k8s ([611bdda](https://github.com/prezero/lvpportal-sf/commit/611bddaa8fdeb49d8e562b21051ffadf866322e8))
* change dockerfile for k8s ([#94](https://github.com/prezero/lvpportal-sf/issues/94)) ([a3e1f18](https://github.com/prezero/lvpportal-sf/commit/a3e1f18bfb5f12507f4ca81dba2623fa2ad3bcc6))
* fix Dockerfile ([232939c](https://github.com/prezero/lvpportal-sf/commit/232939cc937ba176bdabb8c03f195de74b699d30))
* fix target layer ([ab0e1d4](https://github.com/prezero/lvpportal-sf/commit/ab0e1d43ca4c7755796b657cd8a6952eac0d387d))
* remove dedicated monolog env config ([dd42fff](https://github.com/prezero/lvpportal-sf/commit/dd42fffa63ad058ec19b35591883b52fb229f010))

## [2.0.3](https://github.com/prezero/lvpportal-sf/compare/v2.0.2...v2.0.3) (2025-06-18)


### Continuous Integration

* add missing env vars to deploy scripts ([111b44e](https://github.com/prezero/lvpportal-sf/commit/111b44ecfb5e0b3f89516876e27052a35feeb7de))
* fix deploy dev image tag var ([563f135](https://github.com/prezero/lvpportal-sf/commit/563f1354de8c121471874ca9e3128798aababe70))
* use self-hosted runner ([#89](https://github.com/prezero/lvpportal-sf/issues/89)) ([b735377](https://github.com/prezero/lvpportal-sf/commit/b7353775b88d2818a1513d92c752c5a5c237c0d7))

## [2.0.2](https://github.com/prezero/lvpportal-sf/compare/v2.0.1...v2.0.2) (2025-06-17)


### Continuous Integration

* remove old workflows ([101e7ab](https://github.com/prezero/lvpportal-sf/commit/101e7abaf18ca835ec3688b07be2a8373991b32c))

## [2.0.1](https://github.com/prezero/lvpportal-sf/compare/v2.0.0...v2.0.1) (2025-06-17)


### Bug Fixes

* dockerfile ([e8e5472](https://github.com/prezero/lvpportal-sf/commit/e8e5472f154b7a0f6ce87c5350249a6e49f6733c))
* dockerfile ([d786265](https://github.com/prezero/lvpportal-sf/commit/d78626554689afc3932aad001f6492c7eb04aa3f))
* dockerfile ([6b8e8f3](https://github.com/prezero/lvpportal-sf/commit/6b8e8f30398ca25570886fba2674c0918043d147))
* dockerfile ([e26f5ad](https://github.com/prezero/lvpportal-sf/commit/e26f5ad6f2e5d8a3a3a71ca5ca5ad083a67fa451))
* empty contracts ([39fc777](https://github.com/prezero/lvpportal-sf/commit/39fc777f66e398c68a7d6e63d97fe0f3afd9bc47))
* empty contracts ([fd5e664](https://github.com/prezero/lvpportal-sf/commit/fd5e664bb1ec7ef932d51ec8f3e4cecc227a6ec7))
* feiertage ([d05ac40](https://github.com/prezero/lvpportal-sf/commit/d05ac40b1215afb9457cbe2f2dcedf773080b7cd))
* feiertage ([09d5e8a](https://github.com/prezero/lvpportal-sf/commit/09d5e8a7e91b340fbec99ee840edb3d98a28ff30))
* fix runner in the workflows ([e0c1330](https://github.com/prezero/lvpportal-sf/commit/e0c1330d7a38bee511b21cac67f47b9db962dffb))
* fix typo in deploy dev yaml ([789f72f](https://github.com/prezero/lvpportal-sf/commit/789f72fd0869c99f9caa5e7a92702d94b12d8338))
* fix typo in deploy dev yaml ([3261be3](https://github.com/prezero/lvpportal-sf/commit/3261be3444e2abdb5aa84eb1d698ded8b6b9f6f0))
* packages ([68a8446](https://github.com/prezero/lvpportal-sf/commit/68a84462da34aeea1223aa6acb2ad4b47d73d713))
* packages ([b8da401](https://github.com/prezero/lvpportal-sf/commit/b8da4013fe40d7a3ef62a55a03eb148d28be62c4))
* packages ([f1de5c7](https://github.com/prezero/lvpportal-sf/commit/f1de5c781b0918ffd2a75d9300753cfcd5b20e6c))
* packages ([fc1b97d](https://github.com/prezero/lvpportal-sf/commit/fc1b97d2173bd9e1c3a15dab2bd6912c34e92f66))
* packages ([b2ac907](https://github.com/prezero/lvpportal-sf/commit/b2ac907f6ce84a8eb9222737d321f1d66a0ae6fd))
* packages ([49ba27f](https://github.com/prezero/lvpportal-sf/commit/49ba27fc75a40543148e4f42c3d632fa1e3b6120))
* week booking button ([e4c1453](https://github.com/prezero/lvpportal-sf/commit/e4c1453dbbd1e656df999c935a8c2d77590e88c4))
* workflow ([4c48aea](https://github.com/prezero/lvpportal-sf/commit/4c48aeaf87546a188232c76f8af95dc17677aab1))
* workflow ([d78323c](https://github.com/prezero/lvpportal-sf/commit/d78323cf8863da8036dbc93e1ffd3cf7b713070a))


### Continuous Integration

* add release-please workflow ([a56b727](https://github.com/prezero/lvpportal-sf/commit/a56b727e56684d74ae30610c1979d1a38dd31787))
* implement CF image deploy ([#72](https://github.com/prezero/lvpportal-sf/issues/72)) ([d49f719](https://github.com/prezero/lvpportal-sf/commit/d49f7192f257288c44f993b8d130774d326ea2ef))
* Integrate release-please along with the necessary workflows ([194de7a](https://github.com/prezero/lvpportal-sf/commit/194de7a710948debace7d8608ea138bcb78c6723))
* update release-please to use googleapis instead of depricated google-github-actions ([cd3585e](https://github.com/prezero/lvpportal-sf/commit/cd3585ec8268f17d6215ee0bbc07dbfb4dec2532))
* use self-hosted runner ([082445b](https://github.com/prezero/lvpportal-sf/commit/082445b45da6b879f1bd4bf1bd05b80d02e78b07))
* use self-hosted runner ([386987c](https://github.com/prezero/lvpportal-sf/commit/386987cb7ef45a266f2ff84fd0e08d9cea00c505))

## [2.0.0] Initial
